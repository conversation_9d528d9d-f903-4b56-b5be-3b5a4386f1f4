{"version": 3, "sources": ["../../../src/server/app-render/types.ts"], "names": ["flightRouterStateSchema", "dynamicParamTypesSchema", "s", "enums", "segmentSchema", "union", "string", "tuple", "record", "lazy", "optional", "nullable", "literal", "boolean"], "mappings": ";;;;+BA0BaA;;;eAAAA;;;oEAlBC;;;;;;AAId,MAAMC,0BAA0BC,oBAAC,CAACC,KAAK,CAAC;IAAC;IAAK;IAAM;CAAI;AAIxD,MAAMC,gBAAgBF,oBAAC,CAACG,KAAK,CAAC;IAC5BH,oBAAC,CAACI,MAAM;IACRJ,oBAAC,CAACK,KAAK,CAAC;QAACL,oBAAC,CAACI,MAAM;QAAIJ,oBAAC,CAACI,MAAM;QAAIL;KAAwB;CAC1D;AAOM,MAAMD,0BAA2CE,oBAAC,CAACK,KAAK,CAAC;IAC9DH;IACAF,oBAAC,CAACM,MAAM,CACNN,oBAAC,CAACI,MAAM,IACRJ,oBAAC,CAACO,IAAI,CAAC,IAAMT;IAEfE,oBAAC,CAACQ,QAAQ,CAACR,oBAAC,CAACS,QAAQ,CAACT,oBAAC,CAACI,MAAM;IAC9BJ,oBAAC,CAACQ,QAAQ,CAACR,oBAAC,CAACS,QAAQ,CAACT,oBAAC,CAACU,OAAO,CAAC;IAChCV,oBAAC,CAACQ,QAAQ,CAACR,oBAAC,CAACW,OAAO;CACrB"}