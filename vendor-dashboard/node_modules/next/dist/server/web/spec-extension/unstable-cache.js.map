{"version": 3, "sources": ["../../../../src/server/web/spec-extension/unstable-cache.ts"], "names": ["unstable_cache", "cb", "keyParts", "options", "staticGenerationAsyncStorage", "fetch", "__nextGetStaticStore", "_staticGenerationAsyncStorage", "revalidate", "Error", "toString", "cachedCb", "args", "store", "getStore", "maybePostpone", "incrementalCache", "globalThis", "__incrementalCache", "joinedKey", "Array", "isArray", "join", "JSON", "stringify", "run", "fetchCache", "urlPathname", "isUnstableCacheCallback", "isStaticGeneration", "experimental", "ppr", "tags", "validateTags", "tag", "includes", "push", "implicitTags", "addImplicitTags", "cache<PERSON>ey", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cacheEntry", "isOnDemandRevalidate", "get", "softTags", "invokeCallback", "result", "set", "kind", "data", "headers", "body", "status", "url", "CACHE_ONE_YEAR", "value", "console", "error", "cachedValue", "isStale", "resData", "parse", "pendingRevalidates", "catch", "err"], "mappings": ";;;;+BAWgBA;;;eAAAA;;;+BAXc;sDAKgD;2BAC/C;4BACe;AAIvC,SAASA,eACdC,EAAK,EACLC,QAAmB,EACnBC,UAGI,CAAC,CAAC;IAEN,MAAMC,+BACJ,CAAA,AAACC,MAAcC,oBAAoB,oBAAnC,AAACD,MAAcC,oBAAoB,MAAlCD,WAA0CE,kEAA6B;IAE1E,IAAIJ,QAAQK,UAAU,KAAK,GAAG;QAC5B,MAAM,IAAIC,MACR,CAAC,wFAAwF,EAAER,GAAGS,QAAQ,GAAG,CAAC;IAE9G;IAEA,MAAMC,WAAW,OAAO,GAAGC;YAkDdC;QAjDX,MAAMA,QACJT,gDAAAA,6BAA8BU,QAAQ;QAExC,IAAID,SAAS,OAAOV,QAAQK,UAAU,KAAK,UAAU;YACnD,yEAAyE;YACzE,IAAIL,QAAQK,UAAU,KAAK,GAAG;gBAC5BO,IAAAA,4BAAa,EAACF,OAAO;gBACrB,+BAA+B;gBAC/BA,MAAML,UAAU,GAAG;YACnB,gIAAgI;YAClI,OAAO,IAAI,OAAOK,MAAML,UAAU,KAAK,UAAU;gBAC/C,IAAIK,MAAML,UAAU,GAAGL,QAAQK,UAAU,EAAE;oBACzCK,MAAML,UAAU,GAAGL,QAAQK,UAAU;gBACvC;YACA,oDAAoD;YACtD,OAAO;gBACLK,MAAML,UAAU,GAAGL,QAAQK,UAAU;YACvC;QACF;QAEA,MAAMQ,mBAGJH,CAAAA,yBAAAA,MAAOG,gBAAgB,KAAI,AAACC,WAAmBC,kBAAkB;QAEnE,IAAI,CAACF,kBAAkB;YACrB,MAAM,IAAIP,MACR,CAAC,sDAAsD,EAAER,GAAGS,QAAQ,GAAG,CAAC;QAE5E;QAEA,MAAMS,YAAY,CAAC,EAAElB,GAAGS,QAAQ,GAAG,CAAC,EAClCU,MAAMC,OAAO,CAACnB,aAAaA,SAASoB,IAAI,CAAC,KAC1C,CAAC,EAAEC,KAAKC,SAAS,CAACZ,MAAM,CAAC;QAE1B,6DAA6D;QAC7D,oEAAoE;QACpE,oEAAoE;QACpE,0BAA0B;QAC1B,OAAOR,6BAA6BqB,GAAG,CACrC;YACE,GAAGZ,KAAK;YACR,8DAA8D;YAC9D,8CAA8C;YAC9Ca,YAAY;YACZC,aAAad,CAAAA,yBAAAA,MAAOc,WAAW,KAAI;YACnCC,yBAAyB;YACzBC,oBAAoBhB,CAAAA,yBAAAA,MAAOgB,kBAAkB,MAAK;YAClDC,cAAc;gBACZC,KAAKlB,CAAAA,0BAAAA,sBAAAA,MAAOiB,YAAY,qBAAnBjB,oBAAqBkB,GAAG,MAAK;YACpC;QACF,GACA;YACE,MAAMC,OAAOC,IAAAA,wBAAY,EACvB9B,QAAQ6B,IAAI,IAAI,EAAE,EAClB,CAAC,eAAe,EAAE/B,GAAGS,QAAQ,GAAG,CAAC;YAGnC,IAAIU,MAAMC,OAAO,CAACW,SAASnB,OAAO;gBAChC,IAAI,CAACA,MAAMmB,IAAI,EAAE;oBACfnB,MAAMmB,IAAI,GAAG,EAAE;gBACjB;gBACA,KAAK,MAAME,OAAOF,KAAM;oBACtB,IAAI,CAACnB,MAAMmB,IAAI,CAACG,QAAQ,CAACD,MAAM;wBAC7BrB,MAAMmB,IAAI,CAACI,IAAI,CAACF;oBAClB;gBACF;YACF;YACA,MAAMG,eAAexB,QAAQyB,IAAAA,2BAAe,EAACzB,SAAS,EAAE;YAExD,MAAM0B,WAAW,OAAMvB,oCAAAA,iBAAkBwB,aAAa,CAACrB;YACvD,MAAMsB,aACJF,YACA,sDAAsD;YACtD,4CAA4C;YAC5C1B,CAAAA,yBAAAA,MAAOa,UAAU,MAAK,oBACtB,CACEb,CAAAA,CAAAA,yBAAAA,MAAO6B,oBAAoB,KAAI1B,iBAAiB0B,oBAAoB,AAAD,KAEpE,OAAM1B,oCAAAA,iBAAkB2B,GAAG,CAACJ,UAAU;gBACrCb,YAAY;gBACZlB,YAAYL,QAAQK,UAAU;gBAC9BwB;gBACAY,UAAUP;YACZ;YAEF,MAAMQ,iBAAiB;gBACrB,MAAMC,SAAS,MAAM7C,MAAMW;gBAE3B,IAAI2B,YAAYvB,kBAAkB;oBAChC,MAAMA,iBAAiB+B,GAAG,CACxBR,UACA;wBACES,MAAM;wBACNC,MAAM;4BACJC,SAAS,CAAC;4BACV,gCAAgC;4BAChCC,MAAM5B,KAAKC,SAAS,CAACsB;4BACrBM,QAAQ;4BACRC,KAAK;wBACP;wBACA7C,YACE,OAAOL,QAAQK,UAAU,KAAK,WAC1B8C,yBAAc,GACdnD,QAAQK,UAAU;oBAC1B,GACA;wBACEA,YAAYL,QAAQK,UAAU;wBAC9BkB,YAAY;wBACZM;oBACF;gBAEJ;gBACA,OAAOc;YACT;YAEA,IAAI,CAACL,cAAc,CAACA,WAAWc,KAAK,EAAE;gBACpC,OAAOV;YACT;YAEA,IAAIJ,WAAWc,KAAK,CAACP,IAAI,KAAK,SAAS;gBACrCQ,QAAQC,KAAK,CACX,CAAC,0CAA0C,EAAEtC,UAAU,CAAC;gBAE1D,OAAO0B;YACT;YACA,IAAIa;YACJ,MAAMC,UAAUlB,WAAWkB,OAAO;YAElC,IAAIlB,YAAY;gBACd,MAAMmB,UAAUnB,WAAWc,KAAK,CAACN,IAAI;gBACrCS,cAAcnC,KAAKsC,KAAK,CAACD,QAAQT,IAAI;YACvC;YAEA,IAAIQ,SAAS;gBACX,IAAI,CAAC9C,OAAO;oBACV,OAAOgC;gBACT,OAAO;oBACL,IAAI,CAAChC,MAAMiD,kBAAkB,EAAE;wBAC7BjD,MAAMiD,kBAAkB,GAAG,EAAE;oBAC/B;oBACAjD,MAAMiD,kBAAkB,CAAC1B,IAAI,CAC3BS,iBAAiBkB,KAAK,CAAC,CAACC,MACtBR,QAAQC,KAAK,CAAC,CAAC,6BAA6B,EAAEtC,UAAU,CAAC,EAAE6C;gBAGjE;YACF;YACA,OAAON;QACT;IAEJ;IACA,yGAAyG;IACzG,OAAO/C;AACT"}