{"version": 3, "sources": ["../../../src/server/lib/route-resolver.ts"], "names": ["makeResolver", "debug", "setupDebug", "dir", "nextConfig", "middleware", "hostname", "port", "fs<PERSON><PERSON><PERSON>", "setupFsCheck", "dev", "minimalMode", "config", "appDir", "pagesDir", "findPagesDir", "fetchHostname", "formatHostname", "ensure<PERSON><PERSON>back", "item", "result", "type", "Error", "findPageFile", "itemPath", "pageExtensions", "distDir", "path", "join", "middlewareInfo", "name", "paths", "files", "map", "file", "process", "cwd", "wasm", "assets", "length", "middlewareMatcher", "getMiddlewareRouteMatcher", "matcher", "regexp", "originalSource", "resolveRoutes", "getResolveRoutes", "isNodeDebugging", "initialize", "requestHandler", "req", "res", "headers", "cloneableBody", "getCloneableBody", "run", "require", "edgeFunctionEntry", "request", "method", "i18n", "basePath", "trailingSlash", "url", "body", "signal", "signalFromNodeResponse", "useCache", "onWarning", "console", "warn", "err", "upgradeHandler", "deleteAppClientCache", "deleteCache", "clearModuleContext", "propagateServerField", "resolveRoute", "routeResult", "isUpgradeReq", "matchedOutput", "bodyStream", "statusCode", "parsedUrl", "resHeaders", "finished", "pathname", "query", "key", "Object", "keys", "<PERSON><PERSON><PERSON><PERSON>", "destination", "format", "PERMANENT_REDIRECT_STATUS", "end", "pipeToNodeResponse", "protocol", "getRequestMeta", "proxyRequest", "undefined", "cloneBodyStream", "experimental", "proxyTimeout"], "mappings": ";;;;+BAqDs<PERSON>;;;eAAAA;;;QAjDf;4DAES;6DACC;8BACY;6BACE;8DACR;6BACU;8BACJ;4BACA;8BACA;+BACI;2BACS;gCACX;6BACQ;wCACG;8BACP;;;;;;AA+BnC,MAAMC,QAAQC,IAAAA,cAAU,EAAC;AAElB,eAAeF,aACpBG,GAAW,EACXC,UAA8B,EAC9BC,UAA4B,EAC5B,EAAEC,WAAW,WAAW,EAAEC,OAAO,IAAI,EAA0B;IAE/D,MAAMC,YAAY,MAAMC,IAAAA,wBAAY,EAAC;QACnCN;QACAO,KAAK;QACLC,aAAa;QACbC,QAAQR;IACV;IACA,MAAM,EAAES,MAAM,EAAEC,QAAQ,EAAE,GAAGC,IAAAA,0BAAY,EAACZ;IAC1C,mDAAmD;IACnD,MAAMa,gBAAgBC,IAAAA,8BAAc,EAACX;IAErCE,UAAUU,cAAc,CAAC,OAAOC;QAC9B,IAAIC,SAAwB;QAE5B,IAAID,KAAKE,IAAI,KAAK,WAAW;YAC3B,IAAI,CAACR,QAAQ;gBACX,MAAM,IAAIS,MAAM;YAClB;YACAF,SAAS,MAAMG,IAAAA,0BAAY,EACzBV,QACAM,KAAKK,QAAQ,EACbpB,WAAWqB,cAAc,EACzB;QAEJ,OAAO,IAAIN,KAAKE,IAAI,KAAK,YAAY;YACnC,IAAI,CAACP,UAAU;gBACb,MAAM,IAAIQ,MAAM;YAClB;YACAF,SAAS,MAAMG,IAAAA,0BAAY,EACzBT,UACAK,KAAKK,QAAQ,EACbpB,WAAWqB,cAAc,EACzB;QAEJ;QACA,IAAI,CAACL,QAAQ;YACX,MAAM,IAAIE,MAAM,CAAC,yBAAyB,EAAEH,KAAKE,IAAI,CAAC,CAAC,EAAEF,KAAKK,QAAQ,CAAC,CAAC;QAC1E;IACF;IAEA,MAAME,UAAUC,aAAI,CAACC,IAAI,CAACzB,KAAKC,WAAWsB,OAAO;IACjD,MAAMG,iBAAiBxB,aACnB;QACEyB,MAAM;QACNC,OAAO1B,WAAW2B,KAAK,CAACC,GAAG,CAAC,CAACC,OAASP,aAAI,CAACC,IAAI,CAACO,QAAQC,GAAG,IAAIF;QAC/DG,MAAM,EAAE;QACRC,QAAQ,EAAE;IACZ,IACA,CAAC;IAEL,IAAIjC,8BAAAA,WAAY2B,KAAK,CAACO,MAAM,EAAE;YAE1BlC;QADFG,UAAUgC,iBAAiB,GAAGC,IAAAA,iDAAyB,EACrDpC,EAAAA,sBAAAA,WAAWqC,OAAO,qBAAlBrC,oBAAoB4B,GAAG,CAAC,CAACd,OAAU,CAAA;gBACjCwB,QAAQxB;gBACRyB,gBAAgBzB;YAClB,CAAA,OAAO;YAAC;gBAAEwB,QAAQ;gBAAMC,gBAAgB;YAAU;SAAE;IAExD;IAEA,MAAMC,gBAAgBC,IAAAA,+BAAgB,EACpCtC,WACAJ,YACA;QACED;QACAI;QACAD;QACAyC,iBAAiB;QACjBrC,KAAK;IACP,GACA;QACE,MAAMsC;YACJ,OAAO;gBACL,MAAMC,gBAAeC,GAAG,EAAEC,GAAG;oBAC3B,IAAI,CAACD,IAAIE,OAAO,CAAC,sBAAsB,EAAE;wBACvC,MAAM,IAAI9B,MAAM,CAAC,yCAAyC,CAAC;oBAC7D;oBAEA,MAAM+B,gBAAgBC,IAAAA,6BAAgB,EAACJ;oBACvC,MAAM,EAAEK,GAAG,EAAE,GACXC,QAAQ;oBAEV,MAAMpC,SAAS,MAAMmC,IAAI;wBACvB7B;wBACAI,MAAMD,eAAeC,IAAI,IAAI;wBAC7BC,OAAOF,eAAeE,KAAK,IAAI,EAAE;wBACjC0B,mBAAmB5B;wBACnB6B,SAAS;4BACPN,SAASF,IAAIE,OAAO;4BACpBO,QAAQT,IAAIS,MAAM,IAAI;4BACtBvD,YAAY;gCACVwD,MAAMxD,WAAWwD,IAAI;gCACrBC,UAAUzD,WAAWyD,QAAQ;gCAC7BC,eAAe1D,WAAW0D,aAAa;4BACzC;4BACAC,KAAK,CAAC,OAAO,EAAE/C,cAAc,CAAC,EAAET,KAAK,EAAE2C,IAAIa,GAAG,CAAC,CAAC;4BAChDC,MAAMX;4BACNY,QAAQC,IAAAA,mCAAsB,EAACf;wBACjC;wBACAgB,UAAU;wBACVC,WAAWC,QAAQC,IAAI;oBACzB;oBAEA,MAAMC,MAAM,IAAIjD;oBACdiD,IAAYnD,MAAM,GAAGA;oBACvB,MAAMmD;gBACR;gBACA,MAAMC;oBACJ,MAAM,IAAIlD,MAAM,CAAC,0CAA0C,CAAC;gBAC9D;YACF;QACF;QACAmD,yBAAwB;QACxB,MAAMC,gBAAe;QACrB,MAAMC,uBAAsB;QAC5B,MAAMC,yBAAwB;IAChC,GACA,CAAC;IAGH,OAAO,eAAeC,aACpB3B,GAAoB,EACpBC,GAAmB;QAEnB,MAAM2B,cAAc,MAAMjC,cAAc;YACtCK;YACAC;YACA4B,cAAc;YACdd,QAAQC,IAAAA,mCAAsB,EAACf;QACjC;QAEA,MAAM,EACJ6B,aAAa,EACbC,UAAU,EACVC,UAAU,EACVC,SAAS,EACTC,UAAU,EACVC,QAAQ,EACT,GAAGP;QAEJ7E,MAAM,mBAAmBiD,IAAIa,GAAG,EAAE;YAChCiB;YACAE;YACAE;YACAH,YAAY,CAAC,CAACA;YACdE,WAAW;gBACTG,UAAUH,UAAUG,QAAQ;gBAC5BC,OAAOJ,UAAUI,KAAK;YACxB;YACAF;QACF;QAEA,KAAK,MAAMG,OAAOC,OAAOC,IAAI,CAACN,cAAc,CAAC,GAAI;YAC/CjC,IAAIwC,SAAS,CAACH,KAAKJ,UAAU,CAACI,IAAI;QACpC;QAEA,IAAI,CAACP,cAAcC,cAAcA,aAAa,OAAOA,aAAa,KAAK;YACrE,MAAMU,cAAc7B,YAAG,CAAC8B,MAAM,CAACV;YAC/BhC,IAAI+B,UAAU,GAAGA;YACjB/B,IAAIwC,SAAS,CAAC,YAAYC;YAE1B,IAAIV,eAAeY,oCAAyB,EAAE;gBAC5C3C,IAAIwC,SAAS,CAAC,WAAW,CAAC,MAAM,EAAEC,YAAY,CAAC;YACjD;YACAzC,IAAI4C,GAAG,CAACH;YACR;QACF;QAEA,kCAAkC;QAClC,IAAIX,YAAY;YACd9B,IAAI+B,UAAU,GAAGA,cAAc;YAC/B,OAAO,MAAMc,IAAAA,gCAAkB,EAACf,YAAY9B;QAC9C;QAEA,IAAIkC,YAAYF,UAAUc,QAAQ,EAAE;gBAMhCC;YALF,MAAMC,IAAAA,0BAAY,EAChBjD,KACAC,KACAgC,WACAiB,YACAF,kBAAAA,IAAAA,2BAAc,EAAChD,KAAK,oCAApBgD,gBAAqCG,eAAe,IACpDjG,WAAWkG,YAAY,CAACC,YAAY;YAEtC;QACF;QAEApD,IAAIwC,SAAS,CAAC,yBAAyB;QACvCxC,IAAI4C,GAAG;QAEP,OAAO;YACL1E,MAAM;YACN6D,YAAY;YACZ9B,SAASgC;YACTrB,KAAKA,YAAG,CAAC8B,MAAM,CAACV;QAClB;IACF;AACF"}