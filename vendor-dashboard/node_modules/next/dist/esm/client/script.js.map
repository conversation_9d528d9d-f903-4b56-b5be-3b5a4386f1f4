{"version": 3, "sources": ["../../src/client/script.tsx"], "names": ["ReactDOM", "React", "useEffect", "useContext", "useRef", "HeadManagerContext", "DOMAttributeNames", "requestIdleCallback", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Map", "Load<PERSON>ache", "Set", "ignoreProps", "insertStylesheets", "stylesheets", "preinit", "for<PERSON>ach", "stylesheet", "as", "window", "head", "document", "link", "createElement", "type", "rel", "href", "append<PERSON><PERSON><PERSON>", "loadScript", "props", "src", "id", "onLoad", "onReady", "dangerouslySetInnerHTML", "children", "strategy", "onError", "cache<PERSON>ey", "has", "add", "get", "then", "afterLoad", "el", "loadPromise", "Promise", "resolve", "reject", "addEventListener", "e", "call", "catch", "innerHTML", "__html", "textContent", "Array", "isArray", "join", "set", "k", "value", "Object", "entries", "undefined", "includes", "attr", "toLowerCase", "setAttribute", "body", "handleClientScriptLoad", "loadLazyScript", "readyState", "addBeforeInteractiveToCache", "scripts", "querySelectorAll", "script", "getAttribute", "initScriptLoader", "scriptLoaderItems", "<PERSON><PERSON><PERSON>", "restProps", "updateScripts", "getIsSsr", "appDir", "nonce", "hasOnReadyEffectCalled", "current", "hasLoadScriptEffectCalled", "concat", "styleSrc", "JSON", "stringify", "preload", "integrity", "defineProperty"], "mappings": "AAAA;AAEA,OAAOA,cAAc,YAAW;AAChC,OAAOC,SAASC,SAAS,EAAEC,UAAU,EAAEC,MAAM,QAAQ,QAAO;AAE5D,SAASC,kBAAkB,QAAQ,oDAAmD;AACtF,SAASC,iBAAiB,QAAQ,iBAAgB;AAClD,SAASC,mBAAmB,QAAQ,0BAAyB;AAE7D,MAAMC,cAAc,IAAIC;AACxB,MAAMC,YAAY,IAAIC;AAiBtB,MAAMC,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAMC,oBAAoB,CAACC;IACzB,iGAAiG;IACjG,EAAE;IACF,oEAAoE;IACpE,kFAAkF;IAClF,4EAA4E;IAC5E,6EAA6E;IAC7E,IAAId,SAASe,OAAO,EAAE;QACpBD,YAAYE,OAAO,CAAC,CAACC;YACnBjB,SAASe,OAAO,CAACE,YAAY;gBAAEC,IAAI;YAAQ;QAC7C;QAEA;IACF;IAEA,gGAAgG;IAChG,EAAE;IACF,kEAAkE;IAClE,yEAAyE;IACzE,IAAI,OAAOC,WAAW,aAAa;QACjC,IAAIC,OAAOC,SAASD,IAAI;QACxBN,YAAYE,OAAO,CAAC,CAACC;YACnB,IAAIK,OAAOD,SAASE,aAAa,CAAC;YAElCD,KAAKE,IAAI,GAAG;YACZF,KAAKG,GAAG,GAAG;YACXH,KAAKI,IAAI,GAAGT;YAEZG,KAAKO,WAAW,CAACL;QACnB;IACF;AACF;AAEA,MAAMM,aAAa,CAACC;IAClB,MAAM,EACJC,GAAG,EACHC,EAAE,EACFC,SAAS,KAAO,CAAC,EACjBC,UAAU,IAAI,EACdC,uBAAuB,EACvBC,WAAW,EAAE,EACbC,WAAW,kBAAkB,EAC7BC,OAAO,EACPvB,WAAW,EACZ,GAAGe;IAEJ,MAAMS,WAAWP,MAAMD;IAEvB,4BAA4B;IAC5B,IAAIQ,YAAY5B,UAAU6B,GAAG,CAACD,WAAW;QACvC;IACF;IAEA,qDAAqD;IACrD,IAAI9B,YAAY+B,GAAG,CAACT,MAAM;QACxBpB,UAAU8B,GAAG,CAACF;QACd,wGAAwG;QACxG,sGAAsG;QACtG9B,YAAYiC,GAAG,CAACX,KAAKY,IAAI,CAACV,QAAQK;QAClC;IACF;IAEA,0CAA0C,GAC1C,MAAMM,YAAY;QAChB,kDAAkD;QAClD,IAAIV,SAAS;YACXA;QACF;QACA,mDAAmD;QACnDvB,UAAU8B,GAAG,CAACF;IAChB;IAEA,MAAMM,KAAKvB,SAASE,aAAa,CAAC;IAElC,MAAMsB,cAAc,IAAIC,QAAc,CAACC,SAASC;QAC9CJ,GAAGK,gBAAgB,CAAC,QAAQ,SAAUC,CAAC;YACrCH;YACA,IAAIf,QAAQ;gBACVA,OAAOmB,IAAI,CAAC,IAAI,EAAED;YACpB;YACAP;QACF;QACAC,GAAGK,gBAAgB,CAAC,SAAS,SAAUC,CAAC;YACtCF,OAAOE;QACT;IACF,GAAGE,KAAK,CAAC,SAAUF,CAAC;QAClB,IAAIb,SAAS;YACXA,QAAQa;QACV;IACF;IAEA,IAAIhB,yBAAyB;QAC3B,2DAA2D;QAC3DU,GAAGS,SAAS,GAAG,AAACnB,wBAAwBoB,MAAM,IAAe;QAE7DX;IACF,OAAO,IAAIR,UAAU;QACnBS,GAAGW,WAAW,GACZ,OAAOpB,aAAa,WAChBA,WACAqB,MAAMC,OAAO,CAACtB,YACdA,SAASuB,IAAI,CAAC,MACd;QAENf;IACF,OAAO,IAAIb,KAAK;QACdc,GAAGd,GAAG,GAAGA;QACT,4DAA4D;QAC5D,yFAAyF;QAEzFtB,YAAYmD,GAAG,CAAC7B,KAAKe;IACvB;IAEA,KAAK,MAAM,CAACe,GAAGC,MAAM,IAAIC,OAAOC,OAAO,CAAClC,OAAQ;QAC9C,IAAIgC,UAAUG,aAAapD,YAAYqD,QAAQ,CAACL,IAAI;YAClD;QACF;QAEA,MAAMM,OAAO5D,iBAAiB,CAACsD,EAAE,IAAIA,EAAEO,WAAW;QAClDvB,GAAGwB,YAAY,CAACF,MAAML;IACxB;IAEA,IAAIzB,aAAa,UAAU;QACzBQ,GAAGwB,YAAY,CAAC,QAAQ;IAC1B;IAEAxB,GAAGwB,YAAY,CAAC,gBAAgBhC;IAEhC,0CAA0C;IAC1C,IAAItB,aAAa;QACfD,kBAAkBC;IACpB;IAEAO,SAASgD,IAAI,CAAC1C,WAAW,CAACiB;AAC5B;AAEA,OAAO,SAAS0B,uBAAuBzC,KAAkB;IACvD,MAAM,EAAEO,WAAW,kBAAkB,EAAE,GAAGP;IAC1C,IAAIO,aAAa,cAAc;QAC7BjB,OAAO8B,gBAAgB,CAAC,QAAQ;YAC9B1C,oBAAoB,IAAMqB,WAAWC;QACvC;IACF,OAAO;QACLD,WAAWC;IACb;AACF;AAEA,SAAS0C,eAAe1C,KAAkB;IACxC,IAAIR,SAASmD,UAAU,KAAK,YAAY;QACtCjE,oBAAoB,IAAMqB,WAAWC;IACvC,OAAO;QACLV,OAAO8B,gBAAgB,CAAC,QAAQ;YAC9B1C,oBAAoB,IAAMqB,WAAWC;QACvC;IACF;AACF;AAEA,SAAS4C;IACP,MAAMC,UAAU;WACXrD,SAASsD,gBAAgB,CAAC;WAC1BtD,SAASsD,gBAAgB,CAAC;KAC9B;IACDD,QAAQ1D,OAAO,CAAC,CAAC4D;QACf,MAAMtC,WAAWsC,OAAO7C,EAAE,IAAI6C,OAAOC,YAAY,CAAC;QAClDnE,UAAU8B,GAAG,CAACF;IAChB;AACF;AAEA,OAAO,SAASwC,iBAAiBC,iBAAgC;IAC/DA,kBAAkB/D,OAAO,CAACsD;IAC1BG;AACF;AAEA,SAASO,OAAOnD,KAAkB;IAChC,MAAM,EACJE,EAAE,EACFD,MAAM,EAAE,EACRE,SAAS,KAAO,CAAC,EACjBC,UAAU,IAAI,EACdG,WAAW,kBAAkB,EAC7BC,OAAO,EACPvB,WAAW,EACX,GAAGmE,WACJ,GAAGpD;IAEJ,uCAAuC;IACvC,MAAM,EAAEqD,aAAa,EAAER,OAAO,EAAES,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAE,GACvDlF,WAAWE;IAEb;;;;;;;;;;;;;;;;;;;;;;;;;GAyBC,GACD,MAAMiF,yBAAyBlF,OAAO;IAEtCF,UAAU;QACR,MAAMoC,WAAWP,MAAMD;QACvB,IAAI,CAACwD,uBAAuBC,OAAO,EAAE;YACnC,sEAAsE;YACtE,IAAItD,WAAWK,YAAY5B,UAAU6B,GAAG,CAACD,WAAW;gBAClDL;YACF;YAEAqD,uBAAuBC,OAAO,GAAG;QACnC;IACF,GAAG;QAACtD;QAASF;QAAID;KAAI;IAErB,MAAM0D,4BAA4BpF,OAAO;IAEzCF,UAAU;QACR,IAAI,CAACsF,0BAA0BD,OAAO,EAAE;YACtC,IAAInD,aAAa,oBAAoB;gBACnCR,WAAWC;YACb,OAAO,IAAIO,aAAa,cAAc;gBACpCmC,eAAe1C;YACjB;YAEA2D,0BAA0BD,OAAO,GAAG;QACtC;IACF,GAAG;QAAC1D;QAAOO;KAAS;IAEpB,IAAIA,aAAa,uBAAuBA,aAAa,UAAU;QAC7D,IAAI8C,eAAe;YACjBR,OAAO,CAACtC,SAAS,GAAG,AAACsC,CAAAA,OAAO,CAACtC,SAAS,IAAI,EAAE,AAAD,EAAGqD,MAAM,CAAC;gBACnD;oBACE1D;oBACAD;oBACAE;oBACAC;oBACAI;oBACA,GAAG4C,SAAS;gBACd;aACD;YACDC,cAAcR;QAChB,OAAO,IAAIS,YAAYA,YAAY;YACjC,uCAAuC;YACvCzE,UAAU8B,GAAG,CAACT,MAAMD;QACtB,OAAO,IAAIqD,YAAY,CAACA,YAAY;YAClCvD,WAAWC;QACb;IACF;IAEA,uEAAuE;IACvE,IAAIuD,QAAQ;QACV,oFAAoF;QACpF,uEAAuE;QACvE,oEAAoE;QACpE,6EAA6E;QAC7E,EAAE;QACF,yEAAyE;QACzE,+EAA+E;QAC/E,4EAA4E;QAC5E,wGAAwG;QACxG,IAAItE,aAAa;YACfA,YAAYE,OAAO,CAAC,CAAC0E;gBACnB1F,SAASe,OAAO,CAAC2E,UAAU;oBAAExE,IAAI;gBAAQ;YAC3C;QACF;QAEA,2EAA2E;QAC3E,gEAAgE;QAChE,IAAIkB,aAAa,qBAAqB;YACpC,IAAI,CAACN,KAAK;gBACR,yDAAyD;gBACzD,IAAImD,UAAU/C,uBAAuB,EAAE;oBACrC,2DAA2D;oBAC3D+C,UAAU9C,QAAQ,GAAG8C,UAAU/C,uBAAuB,CACnDoB,MAAM;oBACT,OAAO2B,UAAU/C,uBAAuB;gBAC1C;gBAEA,qBACE,oBAAC0C;oBACCS,OAAOA;oBACPnD,yBAAyB;wBACvBoB,QAAQ,AAAC,4CAAyCqC,KAAKC,SAAS,CAAC;4BAC/D;4BACA;gCAAE,GAAGX,SAAS;4BAAC;yBAChB,IAAE;oBACL;;YAGN,OAAO;gBACL,aAAa;gBACbjF,SAAS6F,OAAO,CACd/D,KACAmD,UAAUa,SAAS,GACf;oBAAE5E,IAAI;oBAAU4E,WAAWb,UAAUa,SAAS;gBAAC,IAC/C;oBAAE5E,IAAI;gBAAS;gBAErB,qBACE,oBAAC0D;oBACCS,OAAOA;oBACPnD,yBAAyB;wBACvBoB,QAAQ,AAAC,4CAAyCqC,KAAKC,SAAS,CAAC;4BAC/D9D;yBACD,IAAE;oBACL;;YAGN;QACF,OAAO,IAAIM,aAAa,oBAAoB;YAC1C,IAAIN,KAAK;gBACP,aAAa;gBACb9B,SAAS6F,OAAO,CACd/D,KACAmD,UAAUa,SAAS,GACf;oBAAE5E,IAAI;oBAAU4E,WAAWb,UAAUa,SAAS;gBAAC,IAC/C;oBAAE5E,IAAI;gBAAS;YAEvB;QACF;IACF;IAEA,OAAO;AACT;AAEA4C,OAAOiC,cAAc,CAACf,QAAQ,gBAAgB;IAAEnB,OAAO;AAAK;AAE5D,eAAemB,OAAM"}