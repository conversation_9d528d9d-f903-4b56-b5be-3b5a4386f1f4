{"version": 3, "sources": ["../../../src/client/components/static-generation-bailout.ts"], "names": ["DynamicServerError", "maybePostpone", "staticGenerationAsyncStorage", "StaticGenBailoutError", "Error", "code", "formatErrorMessage", "reason", "opts", "dynamic", "link", "suffix", "staticGenerationBailout", "staticGenerationStore", "getStore", "forceStatic", "dynamicShouldError", "message", "revalidate", "staticPrefetchBailout", "isStaticGeneration", "err", "dynamicUsageDescription", "dynamicUsageStack", "stack"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,aAAa,QAAQ,mBAAkB;AAChD,SAASC,4BAA4B,QAAQ,6CAA4C;AAEzF,MAAMC,8BAA8BC;;;aAClCC,OAAO;;AACT;AASA,SAASC,mBAAmBC,MAAc,EAAEC,IAAkB;IAC5D,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAE,GAAGF,QAAQ,CAAC;IACnC,MAAMG,SAASD,OAAO,AAAC,0BAAuBA,OAAS;IACvD,OAAO,AAAC,SACND,CAAAA,UAAU,AAAC,uBAAqBA,UAAQ,OAAO,EAAC,IACjD,uDAAqDF,SAAO,OAAKI;AACpE;AAEA,OAAO,MAAMC,0BAAmD,CAC9DL,QACAC;IAEA,MAAMK,wBAAwBX,6BAA6BY,QAAQ;IACnE,IAAI,CAACD,uBAAuB,OAAO;IAEnC,IAAIA,sBAAsBE,WAAW,EAAE;QACrC,OAAO;IACT;IAEA,IAAIF,sBAAsBG,kBAAkB,EAAE;YAEKR;QADjD,MAAM,IAAIL,sBACRG,mBAAmBC,QAAQ;YAAE,GAAGC,IAAI;YAAEC,SAASD,CAAAA,gBAAAA,wBAAAA,KAAMC,OAAO,YAAbD,gBAAiB;QAAQ;IAE5E;IAEA,MAAMS,UAAUX,mBAAmBC,QAAQ;QACzC,GAAGC,IAAI;QACP,uEAAuE;QACvE,8EAA8E;QAC9EE,MAAM;IACR;IAEAT,cAAcY,uBAAuBI;IAErC,2EAA2E;IAC3E,QAAQ;IACRJ,sBAAsBK,UAAU,GAAG;IAEnC,IAAI,EAACV,wBAAAA,KAAMC,OAAO,GAAE;QAClB,0DAA0D;QAC1D,sCAAsC;QACtCI,sBAAsBM,qBAAqB,GAAG;IAChD;IAEA,IAAIN,sBAAsBO,kBAAkB,EAAE;QAC5C,MAAMC,MAAM,IAAIrB,mBAAmBiB;QACnCJ,sBAAsBS,uBAAuB,GAAGf;QAChDM,sBAAsBU,iBAAiB,GAAGF,IAAIG,KAAK;QAEnD,MAAMH;IACR;IAEA,OAAO;AACT,EAAC"}