{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-patch-reducer.ts"], "names": ["createHrefFromUrl", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "handleExternalUrl", "applyFlightData", "handleMutable", "serverPatchReducer", "state", "action", "flightData", "previousTree", "overrideCanonicalUrl", "cache", "mutable", "isForCurrentTree", "JSON", "stringify", "tree", "console", "log", "pushRef", "pendingPush", "currentTree", "currentCache", "flightDataPath", "flightSegmentPath", "slice", "treePatch", "newTree", "Error", "canonicalUrl", "canonicalUrlOverrideHref", "undefined", "patchedTree"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,2BAA2B,QAAQ,sCAAqC;AAMjF,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,eAAe,QAAQ,uBAAsB;AACtD,SAASC,aAAa,QAAQ,oBAAmB;AAEjD,OAAO,SAASC,mBACdC,KAA2B,EAC3BC,MAAyB;IAEzB,MAAM,EAAEC,UAAU,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,KAAK,EAAEC,OAAO,EAAE,GACtEL;IAEF,MAAMM,mBACJC,KAAKC,SAAS,CAACN,kBAAkBK,KAAKC,SAAS,CAACT,MAAMU,IAAI;IAE5D,kIAAkI;IAClI,iFAAiF;IACjF,IAAI,CAACH,kBAAkB;QACrB,iCAAiC;QACjCI,QAAQC,GAAG,CAAC;QACZ,yBAAyB;QACzB,OAAOZ;IACT;IAEA,IAAIM,QAAQH,YAAY,EAAE;QACxB,OAAOL,cAAcE,OAAOM;IAC9B;IAEA,4DAA4D;IAC5D,IAAI,OAAOJ,eAAe,UAAU;QAClC,OAAON,kBACLI,OACAM,SACAJ,YACAF,MAAMa,OAAO,CAACC,WAAW;IAE7B;IAEA,IAAIC,cAAcf,MAAMU,IAAI;IAC5B,IAAIM,eAAehB,MAAMK,KAAK;IAE9B,KAAK,MAAMY,kBAAkBf,WAAY;QACvC,mFAAmF;QACnF,MAAMgB,oBAAoBD,eAAeE,KAAK,CAAC,GAAG,CAAC;QAEnD,MAAM,CAACC,UAAU,GAAGH,eAAeE,KAAK,CAAC,CAAC,GAAG,CAAC;QAC9C,MAAME,UAAU3B,4BACd,sBAAsB;QACtB;YAAC;eAAOwB;SAAkB,EAC1BH,aACAK;QAGF,IAAIC,YAAY,MAAM;YACpB,MAAM,IAAIC,MAAM;QAClB;QAEA,IAAI3B,4BAA4BoB,aAAaM,UAAU;YACrD,OAAOzB,kBACLI,OACAM,SACAN,MAAMuB,YAAY,EAClBvB,MAAMa,OAAO,CAACC,WAAW;QAE7B;QAEA,MAAMU,2BAA2BpB,uBAC7BX,kBAAkBW,wBAClBqB;QAEJ,IAAID,0BAA0B;YAC5BlB,QAAQiB,YAAY,GAAGC;QACzB;QAEA3B,gBAAgBmB,cAAcX,OAAOY;QAErCX,QAAQH,YAAY,GAAGY;QACvBT,QAAQoB,WAAW,GAAGL;QACtBf,QAAQD,KAAK,GAAGA;QAEhBW,eAAeX;QACfU,cAAcM;IAChB;IAEA,OAAOvB,cAAcE,OAAOM;AAC9B"}