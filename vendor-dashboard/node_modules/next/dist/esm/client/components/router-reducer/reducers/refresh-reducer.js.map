{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/refresh-reducer.ts"], "names": ["fetchServerResponse", "createRecordFromThenable", "readRecordValue", "createHrefFromUrl", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "handleExternalUrl", "handleMutable", "CacheStates", "fillLazyItemsTillLeafWithHead", "refreshReducer", "state", "action", "cache", "mutable", "origin", "href", "canonicalUrl", "currentTree", "tree", "isForCurrentTree", "JSON", "stringify", "previousTree", "data", "URL", "nextUrl", "buildId", "flightData", "canonicalUrlOverride", "pushRef", "pendingPush", "flightDataPath", "length", "console", "log", "treePatch", "newTree", "Error", "canonicalUrlOverrideHref", "undefined", "subTreeData", "head", "slice", "status", "READY", "prefetchCache", "Map", "patchedTree"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,2BAA0B;AAC9D,SAASC,wBAAwB,QAAQ,iCAAgC;AACzE,SAASC,eAAe,QAAQ,uBAAsB;AACtD,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,2BAA2B,QAAQ,sCAAqC;AAMjF,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,WAAW,QAAQ,2DAA0D;AACtF,SAASC,6BAA6B,QAAQ,yCAAwC;AAEtF,OAAO,SAASC,eACdC,KAA2B,EAC3BC,MAAqB;IAErB,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,EAAE,GAAGH;IACnC,MAAMI,OAAOL,MAAMM,YAAY;IAE/B,IAAIC,cAAcP,MAAMQ,IAAI;IAE5B,MAAMC,mBACJC,KAAKC,SAAS,CAACR,QAAQS,YAAY,MAAMF,KAAKC,SAAS,CAACJ;IAE1D,IAAIE,kBAAkB;QACpB,OAAOb,cAAcI,OAAOG;IAC9B;IAEA,IAAI,CAACD,MAAMW,IAAI,EAAE;QACf,uDAAuD;QACvD,wCAAwC;QACxCX,MAAMW,IAAI,GAAGvB,yBACXD,oBACE,IAAIyB,IAAIT,MAAMD,SACd;YAACG,WAAW,CAAC,EAAE;YAAEA,WAAW,CAAC,EAAE;YAAEA,WAAW,CAAC,EAAE;YAAE;SAAU,EAC3DP,MAAMe,OAAO,EACbf,MAAMgB,OAAO;IAGnB;IACA,MAAM,CAACC,YAAYC,qBAAqB,GAAG3B,gBAAgBW,MAAMW,IAAI;IAErE,4DAA4D;IAC5D,IAAI,OAAOI,eAAe,UAAU;QAClC,OAAOtB,kBACLK,OACAG,SACAc,YACAjB,MAAMmB,OAAO,CAACC,WAAW;IAE7B;IAEA,2DAA2D;IAC3DlB,MAAMW,IAAI,GAAG;IAEb,KAAK,MAAMQ,kBAAkBJ,WAAY;QACvC,oFAAoF;QACpF,IAAII,eAAeC,MAAM,KAAK,GAAG;YAC/B,oCAAoC;YACpCC,QAAQC,GAAG,CAAC;YACZ,OAAOxB;QACT;QAEA,2GAA2G;QAC3G,MAAM,CAACyB,UAAU,GAAGJ;QACpB,MAAMK,UAAUjC,4BACd,sBAAsB;QACtB;YAAC;SAAG,EACJc,aACAkB;QAGF,IAAIC,YAAY,MAAM;YACpB,MAAM,IAAIC,MAAM;QAClB;QAEA,IAAIjC,4BAA4Ba,aAAamB,UAAU;YACrD,OAAO/B,kBAAkBK,OAAOG,SAASE,MAAML,MAAMmB,OAAO,CAACC,WAAW;QAC1E;QAEA,MAAMQ,2BAA2BV,uBAC7B1B,kBAAkB0B,wBAClBW;QAEJ,IAAIX,sBAAsB;YACxBf,QAAQG,YAAY,GAAGsB;QACzB;QAEA,0DAA0D;QAC1D,MAAM,CAACE,aAAaC,KAAK,GAAGV,eAAeW,KAAK,CAAC,CAAC;QAElD,8FAA8F;QAC9F,IAAIF,gBAAgB,MAAM;YACxB5B,MAAM+B,MAAM,GAAGpC,YAAYqC,KAAK;YAChChC,MAAM4B,WAAW,GAAGA;YACpBhC,8BACEI,OACA,4FAA4F;YAC5F2B,WACAJ,WACAM;YAEF5B,QAAQD,KAAK,GAAGA;YAChBC,QAAQgC,aAAa,GAAG,IAAIC;QAC9B;QAEAjC,QAAQS,YAAY,GAAGL;QACvBJ,QAAQkC,WAAW,GAAGX;QACtBvB,QAAQG,YAAY,GAAGD;QAEvBE,cAAcmB;IAChB;IAEA,OAAO9B,cAAcI,OAAOG;AAC9B"}