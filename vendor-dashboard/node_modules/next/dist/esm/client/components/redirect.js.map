{"version": 3, "sources": ["../../../src/client/components/redirect.ts"], "names": ["requestAsyncStorage", "REDIRECT_ERROR_CODE", "RedirectType", "push", "replace", "getRedirectError", "url", "type", "permanent", "error", "Error", "digest", "requestStore", "getStore", "mutableCookies", "redirect", "permanentRedirect", "isRedirectError", "errorCode", "destination", "split", "getURLFromRedirectError", "getRedirectTypeFromError"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,mCAAkC;AAGtE,MAAMC,sBAAsB;WAErB;UAAKC,YAAY;IAAZA,aACVC,UAAAA;IADUD,aAEVE,aAAAA;GAFUF,iBAAAA;AAUZ,OAAO,SAASG,iBACdC,GAAW,EACXC,IAAkB,EAClBC,SAA0B;IAA1BA,IAAAA,sBAAAA,YAAqB;IAErB,MAAMC,QAAQ,IAAIC,MAAMT;IACxBQ,MAAME,MAAM,GAAG,AAAGV,sBAAoB,MAAGM,OAAK,MAAGD,MAAI,MAAGE;IACxD,MAAMI,eAAeZ,oBAAoBa,QAAQ;IACjD,IAAID,cAAc;QAChBH,MAAMK,cAAc,GAAGF,aAAaE,cAAc;IACpD;IACA,OAAOL;AACT;AAEA;;;;;;CAMC,GACD,OAAO,SAASM,SACdT,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,iBAAAA,OA/BU;IAiCV,MAAMF,iBAAiBC,KAAKC,MAAM;AACpC;AAEA;;;;;;CAMC,GACD,OAAO,SAASS,kBACdV,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,iBAAAA,OA7CU;IA+CV,MAAMF,iBAAiBC,KAAKC,MAAM;AACpC;AAEA;;;;;;CAMC,GACD,OAAO,SAASU,gBACdR,KAAU;IAEV,IAAI,QAAOA,yBAAAA,MAAOE,MAAM,MAAK,UAAU,OAAO;IAE9C,MAAM,CAACO,WAAWX,MAAMY,aAAaX,UAAU,GAAG,AAChDC,MAAME,MAAM,CACZS,KAAK,CAAC,KAAK;IAEb,OACEF,cAAcjB,uBACbM,CAAAA,SAAS,aAAaA,SAAS,MAAK,KACrC,OAAOY,gBAAgB,YACtBX,CAAAA,cAAc,UAAUA,cAAc,OAAM;AAEjD;AAYA,OAAO,SAASa,wBAAwBZ,KAAU;IAChD,IAAI,CAACQ,gBAAgBR,QAAQ,OAAO;IAEpC,wEAAwE;IACxE,kBAAkB;IAClB,OAAOA,MAAME,MAAM,CAACS,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEA,OAAO,SAASE,yBACdb,KAAuB;IAEvB,IAAI,CAACQ,gBAAgBR,QAAQ;QAC3B,MAAM,IAAIC,MAAM;IAClB;IAEA,OAAOD,MAAME,MAAM,CAACS,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC"}