{"version": 3, "sources": ["../../src/lib/verify-root-layout.ts"], "names": ["verifyRootLayout", "globOrig", "require", "glob", "cwd", "pattern", "Promise", "resolve", "reject", "err", "files", "getRootLayout", "isTs", "dir", "appDir", "tsconfigPath", "pagePath", "pageExtensions", "rootLayoutPath", "layoutFiles", "join", "isFileUnderAppDir", "startsWith", "APP_DIR_ALIAS", "normalizedPagePath", "replace", "pagePathSegments", "split", "availableDir", "length", "firstSegmentValue", "pop", "currentSegments", "segment", "push", "some", "file", "resolvedTsConfigPath", "path", "hasTsConfig", "fs", "access", "then", "writeFile", "Log", "warn", "bold", "e", "console", "error"], "mappings": ";;;;+BAuDsBA;;;eAAAA;;;6DAvDL;6DACI;oBACU;4BACV;2BACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE9B,MAAMC,WACJC,QAAQ;AACV,MAAMC,OAAO,CAACC,KAAaC;IACzB,OAAO,IAAIC,QAAQ,CAACC,SAASC;QAC3BP,SAASI,SAAS;YAAED;QAAI,GAAG,CAACK,KAAKC;YAC/B,IAAID,KAAK;gBACP,OAAOD,OAAOC;YAChB;YACAF,QAAQG;QACV;IACF;AACF;AAEA,SAASC,cAAcC,IAAa;IAClC,IAAIA,MAAM;QACR,OAAO,CAAC;;;;;;;;;;;;;;;;AAgBZ,CAAC;IACC;IAEA,OAAO,CAAC;;;;;;;;;;;;AAYV,CAAC;AACD;AAEO,eAAeZ,iBAAiB,EACrCa,GAAG,EACHC,MAAM,EACNC,YAAY,EACZC,QAAQ,EACRC,cAAc,EAOf;IACC,IAAIC;IACJ,IAAI;QACF,MAAMC,cAAc,MAAMhB,KACxBW,QACA,CAAC,WAAW,EAAEG,eAAeG,IAAI,CAAC,KAAK,CAAC,CAAC;QAE3C,MAAMC,oBAAoBL,SAASM,UAAU,CAAC,CAAC,EAAEC,wBAAa,CAAC,CAAC,CAAC;QACjE,MAAMC,qBAAqBR,SAASS,OAAO,CAAC,CAAC,EAAEF,wBAAa,CAAC,CAAC,CAAC,EAAE;QACjE,MAAMG,mBAAmBF,mBAAmBG,KAAK,CAAC;QAElD,oGAAoG;QACpG,iDAAiD;QACjD,IAAIC;QAEJ,IAAIP,mBAAmB;YACrB,IAAIF,YAAYU,MAAM,KAAK,GAAG;gBAC5B,+EAA+E;gBAC/E,kGAAkG;gBAClG,uDAAuD;gBACvD,MAAMC,oBAAoBJ,gBAAgB,CAAC,EAAE;gBAC7CE,eAAeE,kBAAkBR,UAAU,CAAC,OACxCQ,oBACA;YACN,OAAO;gBACLJ,iBAAiBK,GAAG,GAAG,gCAAgC;;gBAEvD,IAAIC,kBAA4B,EAAE;gBAClC,KAAK,MAAMC,WAAWP,iBAAkB;oBACtCM,gBAAgBE,IAAI,CAACD;oBACrB,8FAA8F;oBAC9F,IACE,CAACd,YAAYgB,IAAI,CAAC,CAACC,OACjBA,KAAKd,UAAU,CAACU,gBAAgBZ,IAAI,CAAC,QAEvC;wBACAQ,eAAeI,gBAAgBZ,IAAI,CAAC;wBACpC;oBACF;gBACF;YACF;QACF,OAAO;YACLQ,eAAe;QACjB;QAEA,IAAI,OAAOA,iBAAiB,UAAU;YACpC,MAAMS,uBAAuBC,aAAI,CAAClB,IAAI,CAACP,KAAKE;YAC5C,MAAMwB,cAAc,MAAMC,YAAE,CAACC,MAAM,CAACJ,sBAAsBK,IAAI,CAC5D,IAAM,MACN,IAAM;YAGRxB,iBAAiBoB,aAAI,CAAClB,IAAI,CACxBN,QACAc,cACA,CAAC,OAAO,EAAEW,cAAc,QAAQ,KAAK,CAAC;YAExC,MAAMC,YAAE,CAACG,SAAS,CAACzB,gBAAgBP,cAAc4B;YAEjDK,KAAIC,IAAI,CACN,CAAC,UAAU,EAAEC,IAAAA,gBAAI,EACf,CAAC,IAAI,EAAEtB,mBAAmB,CAAC,EAC3B,wCAAwC,EAAEsB,IAAAA,gBAAI,EAC9C,CAAC,GAAG,EAAE5B,eAAeO,OAAO,CAACX,QAAQ,IAAI,CAAC,EAC1C,SAAS,CAAC;YAGd,sBAAsB;YACtB,OAAO;gBAAC;gBAAMI;aAAe;QAC/B;IACF,EAAE,OAAO6B,GAAG;QACVC,QAAQC,KAAK,CAACF;IAChB;IAEA,4BAA4B;IAC5B,OAAO;QAAC;QAAO7B;KAAe;AAChC"}