{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-patch-reducer.ts"], "names": ["serverPatchReducer", "state", "action", "flightData", "previousTree", "overrideCanonicalUrl", "cache", "mutable", "isForCurrentTree", "JSON", "stringify", "tree", "console", "log", "handleMutable", "handleExternalUrl", "pushRef", "pendingPush", "currentTree", "currentCache", "flightDataPath", "flightSegmentPath", "slice", "treePatch", "newTree", "applyRouterStatePatchToTree", "Error", "isNavigatingToNewRootLayout", "canonicalUrl", "canonicalUrlOverrideHref", "createHrefFromUrl", "undefined", "applyFlightData", "patchedTree"], "mappings": ";;;;+BAYgBA;;;eAAAA;;;mCAZkB;6CACU;6CACA;iCAMV;iCACF;+BACF;AAEvB,SAASA,mBACdC,KAA2B,EAC3BC,MAAyB;IAEzB,MAAM,EAAEC,UAAU,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,KAAK,EAAEC,OAAO,EAAE,GACtEL;IAEF,MAAMM,mBACJC,KAAKC,SAAS,CAACN,kBAAkBK,KAAKC,SAAS,CAACT,MAAMU,IAAI;IAE5D,kIAAkI;IAClI,iFAAiF;IACjF,IAAI,CAACH,kBAAkB;QACrB,iCAAiC;QACjCI,QAAQC,GAAG,CAAC;QACZ,yBAAyB;QACzB,OAAOZ;IACT;IAEA,IAAIM,QAAQH,YAAY,EAAE;QACxB,OAAOU,IAAAA,4BAAa,EAACb,OAAOM;IAC9B;IAEA,4DAA4D;IAC5D,IAAI,OAAOJ,eAAe,UAAU;QAClC,OAAOY,IAAAA,kCAAiB,EACtBd,OACAM,SACAJ,YACAF,MAAMe,OAAO,CAACC,WAAW;IAE7B;IAEA,IAAIC,cAAcjB,MAAMU,IAAI;IAC5B,IAAIQ,eAAelB,MAAMK,KAAK;IAE9B,KAAK,MAAMc,kBAAkBjB,WAAY;QACvC,mFAAmF;QACnF,MAAMkB,oBAAoBD,eAAeE,KAAK,CAAC,GAAG,CAAC;QAEnD,MAAM,CAACC,UAAU,GAAGH,eAAeE,KAAK,CAAC,CAAC,GAAG,CAAC;QAC9C,MAAME,UAAUC,IAAAA,wDAA2B,EACzC,sBAAsB;QACtB;YAAC;eAAOJ;SAAkB,EAC1BH,aACAK;QAGF,IAAIC,YAAY,MAAM;YACpB,MAAM,IAAIE,MAAM;QAClB;QAEA,IAAIC,IAAAA,wDAA2B,EAACT,aAAaM,UAAU;YACrD,OAAOT,IAAAA,kCAAiB,EACtBd,OACAM,SACAN,MAAM2B,YAAY,EAClB3B,MAAMe,OAAO,CAACC,WAAW;QAE7B;QAEA,MAAMY,2BAA2BxB,uBAC7ByB,IAAAA,oCAAiB,EAACzB,wBAClB0B;QAEJ,IAAIF,0BAA0B;YAC5BtB,QAAQqB,YAAY,GAAGC;QACzB;QAEAG,IAAAA,gCAAe,EAACb,cAAcb,OAAOc;QAErCb,QAAQH,YAAY,GAAGc;QACvBX,QAAQ0B,WAAW,GAAGT;QACtBjB,QAAQD,KAAK,GAAGA;QAEhBa,eAAeb;QACfY,cAAcM;IAChB;IAEA,OAAOV,IAAAA,4BAAa,EAACb,OAAOM;AAC9B"}