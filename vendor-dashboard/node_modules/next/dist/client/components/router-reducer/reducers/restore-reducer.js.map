{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/restore-reducer.ts"], "names": ["restoreReducer", "state", "action", "url", "tree", "href", "createHrefFromUrl", "buildId", "canonicalUrl", "pushRef", "focusAndScrollRef", "cache", "prefetchCache", "nextUrl", "pathname"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;mCAPkB;AAO3B,SAASA,eACdC,KAA2B,EAC3BC,MAAqB;IAErB,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE,GAAGF;IACtB,MAAMG,OAAOC,IAAAA,oCAAiB,EAACH;IAE/B,OAAO;QACLI,SAASN,MAAMM,OAAO;QACtB,oBAAoB;QACpBC,cAAcH;QACdI,SAASR,MAAMQ,OAAO;QACtBC,mBAAmBT,MAAMS,iBAAiB;QAC1CC,OAAOV,MAAMU,KAAK;QAClBC,eAAeX,MAAMW,aAAa;QAClC,wBAAwB;QACxBR,MAAMA;QACNS,SAASV,IAAIW,QAAQ;IACvB;AACF"}