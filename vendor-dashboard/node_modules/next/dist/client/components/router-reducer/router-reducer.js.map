{"version": 3, "sources": ["../../../../src/client/components/router-reducer/router-reducer.ts"], "names": ["reducer", "clientReducer", "state", "action", "type", "ACTION_NAVIGATE", "navigateReducer", "ACTION_SERVER_PATCH", "serverPatchReducer", "ACTION_RESTORE", "restoreReducer", "ACTION_REFRESH", "refreshReducer", "ACTION_FAST_REFRESH", "fastRefreshReducer", "ACTION_PREFETCH", "prefetchReducer", "ACTION_SERVER_ACTION", "serverActionReducer", "Error", "serverReducer", "_action", "window"], "mappings": ";;;;+BAiEaA;;;eAAAA;;;oCAzDN;iCAMyB;oCACG;gCACJ;gCACA;iCACC;oCACG;qCACC;AAEpC;;CAEC,GACD,SAASC,cACPC,KAA2B,EAC3BC,MAAsB;IAEtB,OAAQA,OAAOC,IAAI;QACjB,KAAKC,mCAAe;YAAE;gBACpB,OAAOC,IAAAA,gCAAe,EAACJ,OAAOC;YAChC;QACA,KAAKI,uCAAmB;YAAE;gBACxB,OAAOC,IAAAA,sCAAkB,EAACN,OAAOC;YACnC;QACA,KAAKM,kCAAc;YAAE;gBACnB,OAAOC,IAAAA,8BAAc,EAACR,OAAOC;YAC/B;QACA,KAAKQ,kCAAc;YAAE;gBACnB,OAAOC,IAAAA,8BAAc,EAACV,OAAOC;YAC/B;QACA,KAAKU,uCAAmB;YAAE;gBACxB,OAAOC,IAAAA,sCAAkB,EAACZ,OAAOC;YACnC;QACA,KAAKY,mCAAe;YAAE;gBACpB,OAAOC,IAAAA,gCAAe,EAACd,OAAOC;YAChC;QACA,KAAKc,wCAAoB;YAAE;gBACzB,OAAOC,IAAAA,wCAAmB,EAAChB,OAAOC;YACpC;QACA,+DAA+D;QAC/D;YACE,MAAM,IAAIgB,MAAM;IACpB;AACF;AAEA,SAASC,cACPlB,KAA2B,EAC3BmB,OAAuB;IAEvB,OAAOnB;AACT;AAGO,MAAMF,UACX,OAAOsB,WAAW,cAAcF,gBAAgBnB"}