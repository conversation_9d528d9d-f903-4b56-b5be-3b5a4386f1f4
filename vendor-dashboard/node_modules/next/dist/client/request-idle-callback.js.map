{"version": 3, "sources": ["../../src/client/request-idle-callback.ts"], "names": ["requestIdleCallback", "cancelIdleCallback", "self", "bind", "window", "cb", "start", "Date", "now", "setTimeout", "didTimeout", "timeRemaining", "Math", "max", "id", "clearTimeout"], "mappings": ";;;;;;;;;;;;;;;IAAaA,mBAAmB;eAAnBA;;IAgBAC,kBAAkB;eAAlBA;;;AAhBN,MAAMD,sBACX,AAAC,OAAOE,SAAS,eACfA,KAAKF,mBAAmB,IACxBE,KAAKF,mBAAmB,CAACG,IAAI,CAACC,WAChC,SAAUC,EAAuB;IAC/B,IAAIC,QAAQC,KAAKC,GAAG;IACpB,OAAON,KAAKO,UAAU,CAAC;QACrBJ,GAAG;YACDK,YAAY;YACZC,eAAe;gBACb,OAAOC,KAAKC,GAAG,CAAC,GAAG,KAAMN,CAAAA,KAAKC,GAAG,KAAKF,KAAI;YAC5C;QACF;IACF,GAAG;AACL;AAEK,MAAML,qBACX,AAAC,OAAOC,SAAS,eACfA,KAAKD,kBAAkB,IACvBC,KAAKD,kBAAkB,CAACE,IAAI,CAACC,WAC/B,SAAUU,EAAU;IAClB,OAAOC,aAAaD;AACtB"}