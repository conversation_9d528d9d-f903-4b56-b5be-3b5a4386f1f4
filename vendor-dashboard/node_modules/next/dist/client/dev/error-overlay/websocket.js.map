{"version": 3, "sources": ["../../../../src/client/dev/error-overlay/websocket.ts"], "names": ["addMessageListener", "sendMessage", "connectHMR", "source", "eventCallbacks", "getSocketProtocol", "assetPrefix", "protocol", "location", "URL", "callback", "push", "data", "readyState", "OPEN", "send", "reconnections", "options", "init", "close", "handleOnline", "window", "console", "log", "handleMessage", "event", "msg", "JSON", "parse", "eventCallback", "timer", "handleDisconnect", "onerror", "onclose", "reload", "clearTimeout", "setTimeout", "hostname", "port", "replace", "url", "startsWith", "split", "WebSocket", "path", "onopen", "onmessage"], "mappings": ";;;;;;;;;;;;;;;;IAmBgBA,kBAAkB;eAAlBA;;IAIAC,WAAW;eAAXA;;IAOAC,UAAU;eAAVA;;;AA5BhB,IAAIC;AAIJ,MAAMC,iBAAwC,EAAE;AAEhD,SAASC,kBAAkBC,WAAmB;IAC5C,IAAIC,WAAWC,SAASD,QAAQ;IAEhC,IAAI;QACF,uBAAuB;QACvBA,WAAW,IAAIE,IAAIH,aAAaC,QAAQ;IAC1C,EAAE,UAAM,CAAC;IAET,OAAOA,aAAa,UAAU,OAAO;AACvC;AAEO,SAASP,mBAAmBU,QAAwB;IACzDN,eAAeO,IAAI,CAACD;AACtB;AAEO,SAAST,YAAYW,IAAY;IACtC,IAAI,CAACT,UAAUA,OAAOU,UAAU,KAAKV,OAAOW,IAAI,EAAE;IAClD,OAAOX,OAAOY,IAAI,CAACH;AACrB;AAEA,IAAII,gBAAgB;AAEb,SAASd,WAAWe,OAA8C;IACvE,SAASC;QACP,IAAIf,QAAQA,OAAOgB,KAAK;QAExB,SAASC;YACPJ,gBAAgB;YAChBK,OAAOC,OAAO,CAACC,GAAG,CAAC;QACrB;QAEA,SAASC,cAAcC,KAA2B;YAChD,sDAAsD;YACtD,MAAMC,MAAwBC,KAAKC,KAAK,CAACH,MAAMb,IAAI;YACnD,KAAK,MAAMiB,iBAAiBzB,eAAgB;gBAC1CyB,cAAcH;YAChB;QACF;QAEA,IAAII;QACJ,SAASC;YACP5B,OAAO6B,OAAO,GAAG;YACjB7B,OAAO8B,OAAO,GAAG;YACjB9B,OAAOgB,KAAK;YACZH;YACA,yGAAyG;YACzG,IAAIA,gBAAgB,IAAI;gBACtBK,OAAOb,QAAQ,CAAC0B,MAAM;gBACtB;YACF;YAEAC,aAAaL;YACb,4BAA4B;YAC5BA,QAAQM,WAAWlB,MAAMF,gBAAgB,IAAI,OAAO;QACtD;QAEA,MAAM,EAAEqB,QAAQ,EAAEC,IAAI,EAAE,GAAG9B;QAC3B,MAAMD,WAAWF,kBAAkBY,QAAQX,WAAW,IAAI;QAC1D,MAAMA,cAAcW,QAAQX,WAAW,CAACiC,OAAO,CAAC,QAAQ;QAExD,IAAIC,MAAM,AAAGjC,WAAS,QAAK8B,WAAS,MAAGC,OACrChC,CAAAA,cAAc,AAAC,MAAGA,cAAgB,EAAC;QAGrC,IAAIA,YAAYmC,UAAU,CAAC,SAAS;YAClCD,MAAM,AAAGjC,WAAS,QAAKD,YAAYoC,KAAK,CAAC,OAAO,EAAE,CAAC,EAAE;QACvD;QAEAvC,SAAS,IAAIkB,OAAOsB,SAAS,CAAC,AAAC,KAAEH,MAAMvB,QAAQ2B,IAAI;QACnDzC,OAAO0C,MAAM,GAAGzB;QAChBjB,OAAO6B,OAAO,GAAGD;QACjB5B,OAAO8B,OAAO,GAAGF;QACjB5B,OAAO2C,SAAS,GAAGtB;IACrB;IAEAN;AACF"}