{"version": 3, "sources": ["../../src/telemetry/project-id.ts"], "names": ["getRawProjectId", "_getProjectIdByGit", "resolve", "reject", "promise", "Promise", "res", "rej", "exec", "timeout", "windowsHide", "error", "stdout", "String", "trim", "_", "process", "env", "REPOSITORY_URL", "cwd"], "mappings": ";;;;+BA0CsBA;;;eAAAA;;;+BA1CD;AAErB,6EAA6E;AAC7E,KAAK;AACL,4EAA4E;AAC5E,2EAA2E;AAC3E,2EAA2E;AAC3E,6EAA6E;AAC7E,gBAAgB;AAChB,4EAA4E;AAC5E,0EAA0E;AAC1E,2CAA2C;AAE3C,eAAeC;IACb,IAAI;QACF,IAAIC,SAA2CC;QAC/C,MAAMC,UAAU,IAAIC,QAAyB,CAACC,KAAKC;YACjDL,UAAUI;YACVH,SAASI;QACX;QAEAC,IAAAA,mBAAI,EACF,CAAC,0CAA0C,CAAC,EAC5C;YACEC,SAAS;YACTC,aAAa;QACf,GACA,CAACC,OAAqBC;YACpB,IAAID,OAAO;gBACTR,OAAOQ;gBACP;YACF;YACAT,QAAQU;QACV;QAGF,OAAOC,OAAO,MAAMT,SAASU,IAAI;IACnC,EAAE,OAAOC,GAAG;QACV,OAAO;IACT;AACF;AAEO,eAAef;IACpB,OACE,AAAC,MAAMC,wBAAyBe,QAAQC,GAAG,CAACC,cAAc,IAAIF,QAAQG,GAAG;AAE7E"}