#!/bin/bash

# ZEDINGO - متجر إلكتروني جزائري متعدد البائعين
# سكريبت التشغيل السريع

echo "🇩🇿 مرحباً بك في ZEDINGO - متجر إلكتروني جزائري متعدد البائعين"
echo "=================================================="

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js غير مثبت. يرجى تثبيت Node.js 18+ أولاً"
    exit 1
fi

# التحقق من وجود PostgreSQL
if ! command -v psql &> /dev/null; then
    echo "❌ PostgreSQL غير مثبت. يرجى تثبيت PostgreSQL أولاً"
    exit 1
fi

echo "✅ Node.js و PostgreSQL متوفران"

# التحقق من وجود قاعدة البيانات
echo "🔍 التحقق من قاعدة البيانات..."
if ! psql -lqt | cut -d \| -f 1 | grep -qw medusa-store; then
    echo "📊 إنشاء قاعدة البيانات..."
    npm run db:create
fi

# تشغيل التهجيرات
echo "🔄 تشغيل التهجيرات..."
npm run db:migrate

# إعداد المتجر الجزائري
echo "🇩🇿 إعداد المتجر الجزائري..."
npm run setup-algeria

echo ""
echo "🚀 تشغيل جميع التطبيقات..."
echo "   - Backend (Medusa): http://localhost:9000"
echo "   - لوحة الإدارة: http://localhost:9000/admin_95"
echo "   - واجهة المتجر: http://localhost:3000"
echo "   - لوحة البائعين: http://localhost:3001"
echo ""
echo "⏳ جاري التشغيل... (قد يستغرق بضع دقائق)"
echo ""

# تشغيل جميع التطبيقات
npm run dev-all
