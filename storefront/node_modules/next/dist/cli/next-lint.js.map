{"version": 3, "sources": ["../../src/cli/next-lint.ts"], "names": ["nextLint", "eslintOptions", "args", "defaultCacheLocation", "overrideConfigFile", "extensions", "resolvePluginsRelativeTo", "rulePaths", "fix", "fixTypes", "ignore<PERSON><PERSON>", "ignore", "Boolean", "allowInlineConfig", "reportUnusedDisableDirectives", "cache", "cacheLocation", "cacheStrategy", "errorOnUnmatchedPattern", "nextConfig", "printAndExit", "baseDir", "getProjectDir", "_", "existsSync", "loadConfig", "PHASE_PRODUCTION_BUILD", "files", "dirs", "eslint", "filesToLint", "pathsToLint", "length", "ESLINT_DEFAULT_DIRS", "reduce", "res", "d", "currDir", "join", "push", "reportErrorsOnly", "maxWarnings", "formatter", "strict", "outputFile", "distDir", "pagesDir", "appDir", "findPagesDir", "verifyTypeScriptSetup", "dir", "intentDirs", "filter", "typeCheckPreflight", "tsconfigPath", "typescript", "disableStaticImages", "images", "hasAppDir", "hasPagesDir", "runLintCheck", "lintDuringBuild", "then", "lintResults", "lintOutput", "output", "eventInfo", "telemetry", "Telemetry", "record", "eventLintCheckCompleted", "buildLint", "flush", "isError", "CompileError", "green", "catch", "err", "message"], "mappings": ";;;;;+BAqMS<PERSON>;;;eAAAA;;;oBAnMkB;sBACN;4BACC;2BAGc;8BACP;uBACA;yBACH;+DACH;4BACgB;wBACC;8BACX;+BACC;8BACD;uCACS;;;;;;AAEtC,MAAMC,gBAAgB,CAACC,MAAgBC,uBAAkC,CAAA;QACvEC,oBAAoBF,IAAI,CAAC,WAAW,IAAI;QACxCG,YAAYH,IAAI,CAAC,QAAQ,IAAI;YAC3B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACDI,0BAA0BJ,IAAI,CAAC,gCAAgC,IAAI;QACnEK,WAAWL,IAAI,CAAC,aAAa,IAAI,EAAE;QACnCM,KAAKN,IAAI,CAAC,QAAQ,IAAI;QACtBO,UAAUP,IAAI,CAAC,aAAa,IAAI;QAChCQ,YAAYR,IAAI,CAAC,gBAAgB,IAAI;QACrCS,QAAQ,CAACC,QAAQV,IAAI,CAAC,cAAc;QACpCW,mBAAmB,CAACD,QAAQV,IAAI,CAAC,qBAAqB;QACtDY,+BACEZ,IAAI,CAAC,qCAAqC,IAAI;QAChDa,OAAO,CAACH,QAAQV,IAAI,CAAC,aAAa;QAClCc,eAAed,IAAI,CAAC,mBAAmB,IAAIC;QAC3Cc,eAAef,IAAI,CAAC,mBAAmB,IAAI;QAC3CgB,yBAAyBhB,IAAI,CAAC,+BAA+B,GACzDU,QAAQV,IAAI,CAAC,+BAA+B,IAC5C;IACN,CAAA;AAEA,MAAMF,WAAuB,OAAOE;QAuEMiB;IAtExC,IAAIjB,IAAI,CAAC,SAAS,EAAE;QAClBkB,IAAAA,mBAAY,EACV,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAqDG,CAAC,EACL;IAEJ;IAEA,MAAMC,UAAUC,IAAAA,4BAAa,EAACpB,KAAKqB,CAAC,CAAC,EAAE;IAEvC,yCAAyC;IACzC,IAAI,CAACC,IAAAA,cAAU,EAACH,UAAU;QACxBD,IAAAA,mBAAY,EAAC,CAAC,gDAAgD,EAAEC,QAAQ,CAAC;IAC3E;IAEA,MAAMF,aAAa,MAAMM,IAAAA,eAAU,EAACC,kCAAsB,EAAEL;IAE5D,MAAMM,QAAkBzB,IAAI,CAAC,SAAS,IAAI,EAAE;IAC5C,MAAM0B,OAAiB1B,IAAI,CAAC,QAAQ,MAAIiB,qBAAAA,WAAWU,MAAM,qBAAjBV,mBAAmBS,IAAI;IAC/D,MAAME,cAAc;WAAKF,QAAQ,EAAE;WAAMD;KAAM;IAE/C,MAAMI,cAAc,AAClBD,CAAAA,YAAYE,MAAM,GAAGF,cAAcG,8BAAmB,AAAD,EACrDC,MAAM,CAAC,CAACC,KAAeC;QACvB,MAAMC,UAAUC,IAAAA,UAAI,EAACjB,SAASe;QAC9B,IAAI,CAACZ,IAAAA,cAAU,EAACa,UAAU,OAAOF;QACjCA,IAAII,IAAI,CAACF;QACT,OAAOF;IACT,GAAG,EAAE;IAEL,MAAMK,mBAAmB5B,QAAQV,IAAI,CAAC,UAAU;IAChD,MAAMuC,cAAcvC,IAAI,CAAC,iBAAiB,IAAI,CAAC;IAC/C,MAAMwC,YAAYxC,IAAI,CAAC,WAAW,IAAI;IACtC,MAAMyC,SAAS/B,QAAQV,IAAI,CAAC,WAAW;IACvC,MAAM0C,aAAa1C,IAAI,CAAC,gBAAgB,IAAI;IAE5C,MAAM2C,UAAUP,IAAAA,UAAI,EAACjB,SAASF,WAAW0B,OAAO;IAChD,MAAM1C,uBAAuBmC,IAAAA,UAAI,EAACO,SAAS,SAAS;IACpD,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAE,GAAGC,IAAAA,0BAAY,EAAC3B;IAE1C,MAAM4B,IAAAA,4CAAqB,EAAC;QAC1BC,KAAK7B;QACLwB,SAAS1B,WAAW0B,OAAO;QAC3BM,YAAY;YAACL;YAAUC;SAAO,CAACK,MAAM,CAACxC;QACtCyC,oBAAoB;QACpBC,cAAcnC,WAAWoC,UAAU,CAACD,YAAY;QAChDE,qBAAqBrC,WAAWsC,MAAM,CAACD,mBAAmB;QAC1DE,WAAW,CAAC,CAACX;QACbY,aAAa,CAAC,CAACb;IACjB;IAEAc,IAAAA,0BAAY,EAACvC,SAASU,aAAa;QACjC8B,iBAAiB;QACjB5D,eAAeA,cAAcC,MAAMC;QACnCqC,kBAAkBA;QAClBC;QACAC;QACAE;QACAD;IACF,GACGmB,IAAI,CAAC,OAAOC;QACX,MAAMC,aACJ,OAAOD,gBAAgB,WAAWA,cAAcA,+BAAAA,YAAaE,MAAM;QAErE,IAAI,OAAOF,gBAAgB,aAAYA,+BAAAA,YAAaG,SAAS,GAAE;YAC7D,MAAMC,YAAY,IAAIC,kBAAS,CAAC;gBAC9BvB;YACF;YACAsB,UAAUE,MAAM,CACdC,IAAAA,+BAAuB,EAAC;gBACtB,GAAGP,YAAYG,SAAS;gBACxBK,WAAW;YACb;YAEF,MAAMJ,UAAUK,KAAK;QACvB;QAEA,IACE,OAAOT,gBAAgB,aACvBA,+BAAAA,YAAaU,OAAO,KACpBT,YACA;YACA,MAAM,IAAIU,0BAAY,CAACV;QACzB;QAEA,IAAIA,YAAY;YACd5C,IAAAA,mBAAY,EAAC4C,YAAY;QAC3B,OAAO,IAAID,eAAe,CAACC,YAAY;YACrC5C,IAAAA,mBAAY,EAACuD,IAAAA,iBAAK,EAAC,mCAAmC;QACxD;IACF,GACCC,KAAK,CAAC,CAACC;QACNzD,IAAAA,mBAAY,EAACyD,IAAIC,OAAO;IAC1B;AACJ"}