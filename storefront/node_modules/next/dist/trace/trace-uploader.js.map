{"version": 3, "sources": ["../../src/trace/trace-uploader.ts"], "names": ["EVENT_FILTER", "Set", "NEXT_TRACE_UPLOAD_DEBUG", "NEXT_TRACE_UPLOAD_FULL", "process", "env", "isDebugEnabled", "shouldUploadFullTrace", "traceUploadUrl", "mode", "_isTurboSession", "projectDir", "distDir", "argv", "isTurboSession", "upload", "nextVersion", "JSON", "parse", "fsPromise", "readFile", "path", "resolve", "__dirname", "version", "telemetry", "Telemetry", "projectPkgJsonPath", "findUp", "assert", "projectPkgJson", "pkgName", "name", "commit", "child_process", "spawnSync", "os", "platform", "shell", "stdout", "toString", "trimEnd", "readLineInterface", "createInterface", "input", "createReadStream", "join", "crlfDelay", "Infinity", "traces", "Map", "line", "lineEvents", "event", "parentId", "undefined", "has", "tags", "trigger", "isAbsolute", "relative", "replaceAll", "sep", "trace", "get", "traceId", "set", "push", "body", "metadata", "anonymousId", "arch", "cpus", "length", "sessionId", "values", "console", "log", "stringify", "res", "fetch", "method", "headers", "status", "json"], "mappings": ";;;;+DAAmB;iEACG;sEACI;+DACP;kEACD;2DACH;0BACiB;oBACC;6DAChB;yBACS;;;;;;AAE1B,iEAAiE;AACjE,yEAAyE;AACzE,yCAAyC;AACzC,MAAMA,eAAe,IAAIC,IAAI;IAC3B;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,EACJC,uBAAuB,EACvB,uFAAuF;AACvF,sGAAsG;AACtG,mFAAmF;AACnFC,sBAAsB,EACvB,GAAGC,QAAQC,GAAG;AAEf,MAAMC,iBAAiB,CAAC,CAACJ,2BAA2B,CAAC,CAACC;AACtD,MAAMI,wBAAwB,CAAC,CAACJ;AAEhC,MAAM,KAAKK,gBAAgBC,MAAMC,iBAAiBC,YAAYC,QAAQ,GACpER,QAAQS,IAAI;AACd,MAAMC,iBAAiBJ,oBAAoB;AA+BzC,CAAA,eAAeK;IACf,MAAMC,cAAcC,KAAKC,KAAK,CAC5B,MAAMC,iBAAS,CAACC,QAAQ,CACtBC,aAAI,CAACC,OAAO,CAACC,WAAW,uBACxB,SAEFC,OAAO;IAET,MAAMC,YAAY,IAAIC,kBAAS,CAAC;QAAEd;IAAQ;IAE1C,MAAMe,qBAAqB,MAAMC,IAAAA,eAAM,EAAC;IACxCC,IAAAA,eAAM,EAACF;IAEP,MAAMG,iBAAiBb,KAAKC,KAAK,CAC/B,MAAMC,iBAAS,CAACC,QAAQ,CAACO,oBAAoB;IAE/C,MAAMI,UAAUD,eAAeE,IAAI;IAEnC,MAAMC,SAASC,sBAAa,CACzBC,SAAS,CACRC,WAAE,CAACC,QAAQ,OAAO,UAAU,YAAY,OACxC;QAAC;QAAa;KAAO,EACrB;QAAEC,OAAO;IAAK,GAEfC,MAAM,CAACC,QAAQ,GACfC,OAAO;IAEV,MAAMC,oBAAoBC,IAAAA,yBAAe,EAAC;QACxCC,OAAOC,IAAAA,oBAAgB,EAACxB,aAAI,CAACyB,IAAI,CAACnC,YAAYC,SAAS;QACvDmC,WAAWC;IACb;IAEA,MAAMC,SAAS,IAAIC;IACnB,WAAW,MAAMC,QAAQT,kBAAmB;QAC1C,MAAMU,aAA2BnC,KAAKC,KAAK,CAACiC;QAC5C,KAAK,MAAME,SAASD,WAAY;YAC9B,IACE,4BAA4B;YAC5BC,MAAMC,QAAQ,KAAKC,aACnBhD,yBACAP,aAAawD,GAAG,CAACH,MAAMrB,IAAI,GAC3B;gBACA,IACE,OAAOqB,MAAMI,IAAI,CAACC,OAAO,KAAK,YAC9BrC,aAAI,CAACsC,UAAU,CAACN,MAAMI,IAAI,CAACC,OAAO,GAClC;oBACAL,MAAMI,IAAI,CAACC,OAAO,GAChB,eACArC,aAAI,CACDuC,QAAQ,CAACjD,YAAY0C,MAAMI,IAAI,CAACC,OAAO,EACvCG,UAAU,CAACxC,aAAI,CAACyC,GAAG,EAAE;gBAC5B;gBAEA,IAAIC,QAAQd,OAAOe,GAAG,CAACX,MAAMY,OAAO;gBACpC,IAAIF,UAAUR,WAAW;oBACvBQ,QAAQ,EAAE;oBACVd,OAAOiB,GAAG,CAACb,MAAMY,OAAO,EAAEF;gBAC5B;gBACAA,MAAMI,IAAI,CAACd;YACb;QACF;IACF;IAEA,MAAMe,OAAyB;QAC7BC,UAAU;YACRC,aAAa7C,UAAU6C,WAAW;YAClCC,MAAMnC,WAAE,CAACmC,IAAI;YACbtC;YACAuC,MAAMpC,WAAE,CAACoC,IAAI,GAAGC,MAAM;YACtB3D;YACAL;YACAO;YACAe;YACAM,UAAUD,WAAE,CAACC,QAAQ;YACrBqC,WAAWjD,UAAUiD,SAAS;QAChC;QACAzB,QAAQ;eAAIA,OAAO0B,MAAM;SAAG;IAC9B;IAEA,IAAIrE,gBAAgB;QAClBsE,QAAQC,GAAG,CAAC,6BAA6B5D,KAAK6D,SAAS,CAACV,MAAM,MAAM;IACtE;IAEA,IAAIW,MAAM,MAAMC,IAAAA,kBAAK,EAACxE,gBAAgB;QACpCyE,QAAQ;QACRC,SAAS;YACP,gBAAgB;YAChB,yBAAyB3E,wBAAwB,SAAS;QAC5D;QACA6D,MAAMnD,KAAK6D,SAAS,CAACV;IACvB;IAEA,IAAI9D,gBAAgB;QAClBsE,QAAQC,GAAG,CAAC,qBAAqBE,IAAII,MAAM,EAAE,MAAMJ,IAAIK,IAAI;IAC7D;AACF,CAAA"}