{"name": "storefront", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start -p 3000", "lint": "next lint"}, "dependencies": {"next": "14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "axios": "^1.6.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "react-hot-toast": "^2.4.1", "swiper": "^11.0.0"}, "devDependencies": {"@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.2.0", "eslint": "^8.51.0", "eslint-config-next": "14.0.0"}}