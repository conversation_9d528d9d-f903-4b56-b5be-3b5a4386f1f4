import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import VendorModuleService from "../../../modules/vendor/service"
import { VENDOR_MODULE } from "../../../modules/vendor"

// Get all approved vendors for public display
export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const vendorModuleService: VendorModuleService = req.scope.resolve(
    VENDOR_MODULE
  )

  const { limit = 20, offset = 0 } = req.query

  const [vendors, count] = await vendorModuleService.listVendors(
    { status: "approved", is_active: true },
    {
      skip: offset,
      take: limit,
    }
  )

  res.json({
    vendors,
    count,
    offset,
    limit,
  })
}

// Register new vendor
export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const vendorModuleService: VendorModuleService = req.scope.resolve(
    VENDOR_MODULE
  )

  // Hash password before saving (you'll need to implement password hashing)
  const vendorData = {
    ...req.body,
    status: "pending", // New vendors start as pending
  }

  const vendor = await vendorModuleService.createVendor(vendorData)

  res.status(201).json({
    vendor: {
      ...vendor,
      password_hash: undefined, // Don't return password hash
    },
  })
}
