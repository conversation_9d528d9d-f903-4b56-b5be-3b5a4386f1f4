import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import VendorModuleService from "../../../modules/vendor/service"
import { VENDOR_MODULE } from "../../../modules/vendor"

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const vendorModuleService: VendorModuleService = req.scope.resolve(
    VENDOR_MODULE
  )

  const { limit = 20, offset = 0, ...filters } = req.query

  const [vendors, count] = await vendorModuleService.listVendors(
    filters,
    {
      skip: offset,
      take: limit,
    }
  )

  res.json({
    vendors,
    count,
    offset,
    limit,
  })
}

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const vendorModuleService: VendorModuleService = req.scope.resolve(
    VENDOR_MODULE
  )

  const vendor = await vendorModuleService.createVendor(req.body)

  res.status(201).json({
    vendor,
  })
}
