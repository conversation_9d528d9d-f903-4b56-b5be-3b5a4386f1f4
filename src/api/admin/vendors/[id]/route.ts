import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import VendorModuleService from "../../../../modules/vendor/service"
import { VENDOR_MODULE } from "../../../../modules/vendor"

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const vendorModuleService: VendorModuleService = req.scope.resolve(
    VENDOR_MODULE
  )

  const { id } = req.params

  const vendor = await vendorModuleService.getVendor(id)

  res.json({
    vendor,
  })
}

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const vendorModuleService: VendorModuleService = req.scope.resolve(
    VENDOR_MODULE
  )

  const { id } = req.params

  const vendor = await vendorModuleService.updateVendor(id, req.body)

  res.json({
    vendor,
  })
}

export async function DELETE(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const vendorModuleService: VendorModuleService = req.scope.resolve(
    VENDOR_MODULE
  )

  const { id } = req.params

  await vendorModuleService.deleteVendor(id)

  res.status(200).json({
    id,
    deleted: true,
  })
}
