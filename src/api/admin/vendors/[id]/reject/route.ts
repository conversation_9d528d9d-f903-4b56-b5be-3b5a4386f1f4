import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import VendorModuleService from "../../../../../modules/vendor/service"
import { VENDOR_MODULE } from "../../../../../modules/vendor"

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const vendorModuleService: VendorModuleService = req.scope.resolve(
    VENDOR_MODULE
  )

  const { id } = req.params

  const vendor = await vendorModuleService.rejectVendor(id)

  res.json({
    vendor,
  })
}
