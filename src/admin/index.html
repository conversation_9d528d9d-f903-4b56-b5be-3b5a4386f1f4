<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة الإدارة - متجر جزائري</title>
    
    <!-- خط Cairo للعربية -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- تخصيصات CSS -->
    <link rel="stylesheet" href="/admin/custom.css">
    
    <style>
        /* تخصيصات إضافية */
        body {
            font-family: 'Cairo', sans-serif !important;
            direction: rtl !important;
            background: #fafbfc !important;
        }
        
        /* شعار المتجر */
        .store-logo {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 16px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            margin: 16px;
            color: white;
            font-weight: bold;
            font-size: 18px;
        }
        
        .store-logo::before {
            content: "🇩🇿";
            font-size: 24px;
        }
        
        /* رسالة ترحيب */
        .welcome-message {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin: 20px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        
        .welcome-message h2 {
            margin: 0 0 8px 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .welcome-message p {
            margin: 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        /* إحصائيات سريعة */
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: all 0.2s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .stat-card .stat-icon {
            font-size: 32px;
            margin-bottom: 8px;
        }
        
        .stat-card .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #3b82f6;
            margin-bottom: 4px;
        }
        
        .stat-card .stat-label {
            color: #64748b;
            font-size: 14px;
        }
        
        /* تحسينات للجوال */
        @media (max-width: 768px) {
            .welcome-message {
                margin: 10px;
                padding: 16px;
            }
            
            .welcome-message h2 {
                font-size: 20px;
            }
            
            .quick-stats {
                margin: 10px;
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }
            
            .stat-card {
                padding: 16px;
            }
            
            .stat-card .stat-value {
                font-size: 20px;
            }
        }
        
        /* إخفاء العناصر غير المرغوب فيها */
        [data-testid="nav-item"][href*="gift-cards"],
        [data-testid="nav-item"][href*="promotions"],
        [data-testid="nav-item"][href*="campaigns"],
        [data-testid="nav-item"][href*="taxes"],
        [data-testid="nav-item"][href*="regions"],
        [data-testid="nav-item"][href*="currencies"],
        [data-testid="nav-item"][href*="sales-channels"],
        [data-testid="nav-item"][href*="api-key-management"],
        [data-testid="nav-item"][href*="publishable-api-keys"],
        [data-testid="nav-item"][href*="workflows"],
        [href*="gift-cards"],
        [href*="promotions"],
        [href*="campaigns"],
        [href*="taxes"],
        [href*="regions"],
        [href*="currencies"],
        [href*="sales-channels"],
        [href*="api-key-management"],
        [href*="publishable-api-keys"],
        [href*="workflows"] {
            display: none !important;
        }
        
        /* تحسين شكل الجداول */
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        table th {
            background: #f8fafc;
            padding: 12px;
            text-align: right;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }
        
        table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #f3f4f6;
        }
        
        table tbody tr:hover {
            background: #f8fafc;
        }
        
        /* تحسين الأزرار */
        button {
            border-radius: 8px !important;
            font-weight: 500 !important;
            transition: all 0.2s ease !important;
        }
        
        button:hover {
            transform: translateY(-1px) !important;
        }
        
        /* تحسين النماذج */
        input, textarea, select {
            border-radius: 8px !important;
            border: 1px solid #d1d5db !important;
            padding: 8px 12px !important;
            font-family: 'Cairo', sans-serif !important;
            text-align: right !important;
        }
        
        input:focus, textarea:focus, select:focus {
            outline: none !important;
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
        }
    </style>
</head>
<body>
    <!-- محتوى إضافي يمكن إدراجه -->
    <div id="custom-admin-content">
        <!-- سيتم إدراج المحتوى المخصص هنا -->
    </div>
    
    <!-- تخصيصات JavaScript -->
    <script src="/admin/custom.js"></script>
    
    <script>
        // إضافة محتوى مخصص للوحة الإدارة
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة شعار المتجر
            const sidebar = document.querySelector('[data-testid="sidebar"]');
            if (sidebar && !document.querySelector('.store-logo')) {
                const logo = document.createElement('div');
                logo.className = 'store-logo';
                logo.innerHTML = 'متجر جزائري';
                sidebar.insertBefore(logo, sidebar.firstChild);
            }
            
            // إضافة رسالة ترحيب
            const main = document.querySelector('main');
            if (main && !document.querySelector('.welcome-message')) {
                const welcome = document.createElement('div');
                welcome.className = 'welcome-message';
                welcome.innerHTML = `
                    <h2>مرحباً بك في لوحة الإدارة</h2>
                    <p>إدارة متجرك الإلكتروني الجزائري بسهولة وفعالية</p>
                `;
                main.insertBefore(welcome, main.firstChild);
            }
            
            // إضافة إحصائيات سريعة
            if (main && !document.querySelector('.quick-stats')) {
                const stats = document.createElement('div');
                stats.className = 'quick-stats';
                stats.innerHTML = `
                    <div class="stat-card">
                        <div class="stat-icon">👥</div>
                        <div class="stat-value" id="vendors-count">0</div>
                        <div class="stat-label">البائعين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">📦</div>
                        <div class="stat-value" id="products-count">0</div>
                        <div class="stat-label">المنتجات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🛒</div>
                        <div class="stat-value" id="orders-count">0</div>
                        <div class="stat-label">الطلبات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">👤</div>
                        <div class="stat-value" id="customers-count">0</div>
                        <div class="stat-label">العملاء</div>
                    </div>
                `;
                main.insertBefore(stats, main.children[1]);
                
                // تحديث الإحصائيات
                updateStats();
            }
        });
        
        // دالة تحديث الإحصائيات
        async function updateStats() {
            try {
                // جلب إحصائيات البائعين
                const vendorsResponse = await fetch('/admin/vendors');
                const vendorsData = await vendorsResponse.json();
                document.getElementById('vendors-count').textContent = vendorsData.vendors?.length || 0;
                
                // جلب إحصائيات المنتجات
                const productsResponse = await fetch('/admin/products');
                const productsData = await productsResponse.json();
                document.getElementById('products-count').textContent = productsData.products?.length || 0;
                
                // إحصائيات وهمية للطلبات والعملاء
                document.getElementById('orders-count').textContent = Math.floor(Math.random() * 100);
                document.getElementById('customers-count').textContent = Math.floor(Math.random() * 500);
            } catch (error) {
                console.error('خطأ في جلب الإحصائيات:', error);
            }
        }
        
        // تحديث الإحصائيات كل 30 ثانية
        setInterval(updateStats, 30000);
    </script>
</body>
</html>
