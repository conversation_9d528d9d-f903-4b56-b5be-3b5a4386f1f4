import { defineWidgetConfig } from "@medusajs/admin-sdk"
import { Container, Heading } from "@medusajs/ui"
import { useState, useEffect } from "react"

const QuickStatsWidget = () => {
  const [stats, setStats] = useState({
    totalVendors: 0,
    pendingVendors: 0,
    totalProducts: 0,
    totalOrders: 0,
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Fetch vendors
        const vendorsResponse = await fetch('/admin/vendors')
        const vendorsData = await vendorsResponse.json()
        const vendors = vendorsData.vendors || []

        // Fetch products (mock for now)
        const productsResponse = await fetch('/admin/products')
        const productsData = await productsResponse.json()
        const products = productsData.products || []

        setStats({
          totalVendors: vendors.length,
          pendingVendors: vendors.filter((v: any) => v.status === 'pending').length,
          totalProducts: products.length,
          totalOrders: Math.floor(Math.random() * 100), // Mock data
        })
      } catch (error) {
        console.error('Error fetching stats:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  if (loading) {
    return (
      <Container className="p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-32 mb-4"></div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-gray-200 h-20 rounded"></div>
            ))}
          </div>
        </div>
      </Container>
    )
  }

  const statCards = [
    {
      title: "البائعين",
      value: stats.totalVendors,
      color: "bg-blue-500",
      textColor: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: "معلق",
      value: stats.pendingVendors,
      color: "bg-yellow-500",
      textColor: "text-yellow-600",
      bgColor: "bg-yellow-50",
    },
    {
      title: "المنتجات",
      value: stats.totalProducts,
      color: "bg-green-500",
      textColor: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      title: "الطلبات",
      value: stats.totalOrders,
      color: "bg-purple-500",
      textColor: "text-purple-600",
      bgColor: "bg-purple-50",
    },
  ]

  return (
    <Container className="p-6">
      <Heading level="h3" className="mb-4 text-right">
        إحصائيات سريعة
      </Heading>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {statCards.map((stat, index) => (
          <div
            key={index}
            className={`${stat.bgColor} p-4 rounded-lg text-center border`}
          >
            <div className={`text-2xl font-bold ${stat.textColor} mb-1`}>
              {stat.value}
            </div>
            <div className={`text-sm ${stat.textColor} opacity-80`}>
              {stat.title}
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 text-center">
        <button
          onClick={() => window.location.href = '/app/vendors'}
          className="text-blue-600 hover:text-blue-700 text-sm font-medium"
        >
          عرض جميع البائعين ←
        </button>
      </div>
    </Container>
  )
}

export const config = defineWidgetConfig({
  zone: "dashboard.before",
})

export default QuickStatsWidget
