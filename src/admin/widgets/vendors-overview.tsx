import { defineWidgetConfig } from "@medusajs/admin-sdk"
import { Container, Heading } from "@medusajs/ui"
import { useState, useEffect } from "react"

const VendorsOverviewWidget = () => {
  const [vendors, setVendors] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchVendors = async () => {
      try {
        const response = await fetch('/admin/vendors')
        const data = await response.json()
        setVendors(data.vendors || [])
      } catch (error) {
        console.error('Error fetching vendors:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchVendors()
  }, [])

  const pendingVendors = vendors.filter((v: any) => v.status === 'pending')
  const approvedVendors = vendors.filter((v: any) => v.status === 'approved')

  if (loading) {
    return (
      <Container className="p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-48 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </Container>
    )
  }

  return (
    <Container className="p-6">
      <Heading level="h2" className="mb-4 text-right">
        نظرة عامة على البائعين
      </Heading>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg text-right">
          <div className="text-2xl font-bold text-blue-600">{vendors.length}</div>
          <div className="text-sm text-blue-800">إجمالي البائعين</div>
        </div>
        
        <div className="bg-yellow-50 p-4 rounded-lg text-right">
          <div className="text-2xl font-bold text-yellow-600">{pendingVendors.length}</div>
          <div className="text-sm text-yellow-800">في انتظار الموافقة</div>
        </div>
        
        <div className="bg-green-50 p-4 rounded-lg text-right">
          <div className="text-2xl font-bold text-green-600">{approvedVendors.length}</div>
          <div className="text-sm text-green-800">البائعين المُوافق عليهم</div>
        </div>
      </div>

      {pendingVendors.length > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="font-medium text-yellow-800 mb-2 text-right">
            بائعين يحتاجون موافقة ({pendingVendors.length})
          </h3>
          <div className="space-y-2">
            {pendingVendors.slice(0, 3).map((vendor: any) => (
              <div key={vendor.id} className="flex justify-between items-center text-sm">
                <div className="flex space-x-2 space-x-reverse">
                  <button 
                    className="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700"
                    onClick={() => window.location.href = `/app/vendors/${vendor.id}`}
                  >
                    عرض
                  </button>
                </div>
                <div className="text-right">
                  <div className="font-medium">{vendor.name}</div>
                  <div className="text-gray-600">{vendor.store_name}</div>
                </div>
              </div>
            ))}
          </div>
          {pendingVendors.length > 3 && (
            <div className="mt-3 text-center">
              <button 
                className="text-yellow-700 hover:text-yellow-800 text-sm font-medium"
                onClick={() => window.location.href = '/app/vendors'}
              >
                عرض جميع البائعين المعلقين
              </button>
            </div>
          )}
        </div>
      )}
    </Container>
  )
}

export const config = defineWidgetConfig({
  zone: "dashboard.after",
})

export default VendorsOverviewWidget
