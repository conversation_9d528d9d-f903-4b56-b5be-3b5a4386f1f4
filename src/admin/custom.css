/* تخصيص لوحة الإدارة للمتجر الجزائري */

/* الألوان الأساسية */
:root {
  --primary-color: #3b82f6;
  --primary-light: #93c5fd;
  --primary-dark: #1d4ed8;
  --secondary-color: #e0f2fe;
  --accent-color: #0ea5e9;
  --background-light: #f0f9ff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
}

/* إعادة تعيين الخط للعربية */
* {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  direction: rtl;
}

/* تخصيص الشريط الجانبي */
[data-testid="sidebar"] {
  background: linear-gradient(180deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
  border-right: none !important;
}

[data-testid="sidebar"] a {
  color: white !important;
  border-radius: 8px !important;
  margin: 4px 8px !important;
  transition: all 0.2s ease !important;
}

[data-testid="sidebar"] a:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  transform: translateX(-2px) !important;
}

[data-testid="sidebar"] a[data-active="true"] {
  background-color: rgba(255, 255, 255, 0.2) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* تخصيص الهيدر */
header {
  background: white !important;
  border-bottom: 1px solid var(--border-color) !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* تخصيص الأزرار */
button[data-variant="primary"] {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: white !important;
}

button[data-variant="primary"]:hover {
  background: var(--primary-dark) !important;
  border-color: var(--primary-dark) !important;
}

button[data-variant="secondary"] {
  background: var(--secondary-color) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

/* تخصيص الجداول */
table {
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

table thead {
  background: var(--background-light) !important;
}

table th {
  color: var(--text-primary) !important;
  font-weight: 600 !important;
  text-align: right !important;
}

table td {
  text-align: right !important;
}

table tbody tr:hover {
  background: var(--background-light) !important;
}

/* تخصيص البطاقات */
[data-testid="card"], .card {
  border-radius: 12px !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  background: white !important;
}

/* تخصيص النماذج */
input, textarea, select {
  border: 1px solid var(--border-color) !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  text-align: right !important;
}

input:focus, textarea:focus, select:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  outline: none !important;
}

/* تخصيص الشارات */
[data-testid="badge"] {
  border-radius: 20px !important;
  font-weight: 500 !important;
  font-size: 12px !important;
  padding: 4px 12px !important;
}

[data-variant="green"] {
  background: var(--success-color) !important;
  color: white !important;
}

[data-variant="orange"] {
  background: var(--warning-color) !important;
  color: white !important;
}

[data-variant="red"] {
  background: var(--error-color) !important;
  color: white !important;
}

/* إخفاء العناصر غير المطلوبة */
[data-testid="nav-item"][href*="gift-cards"],
[data-testid="nav-item"][href*="promotions"],
[data-testid="nav-item"][href*="campaigns"],
[data-testid="nav-item"][href*="taxes"],
[data-testid="nav-item"][href*="regions"],
[data-testid="nav-item"][href*="currencies"],
[data-testid="nav-item"][href*="sales-channels"],
[data-testid="nav-item"][href*="api-key-management"],
[data-testid="nav-item"][href*="publishable-api-keys"],
[data-testid="nav-item"][href*="workflows"] {
  display: none !important;
}

/* تخصيص الإحصائيات */
.stats-card {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%) !important;
  color: white !important;
  border-radius: 12px !important;
  padding: 20px !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

.stats-card h3 {
  color: white !important;
  opacity: 0.9;
}

.stats-card .stats-value {
  font-size: 2rem !important;
  font-weight: bold !important;
  color: white !important;
}

/* تخصيص التنبيهات */
[data-testid="alert"] {
  border-radius: 8px !important;
  border: none !important;
  padding: 16px !important;
}

[data-variant="success"] {
  background: rgba(16, 185, 129, 0.1) !important;
  color: var(--success-color) !important;
}

[data-variant="warning"] {
  background: rgba(245, 158, 11, 0.1) !important;
  color: var(--warning-color) !important;
}

[data-variant="error"] {
  background: rgba(239, 68, 68, 0.1) !important;
  color: var(--error-color) !important;
}

/* تخصيص القوائم المنسدلة */
[data-testid="dropdown"] {
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid var(--border-color) !important;
}

/* تخصيص المودال */
[data-testid="modal"] {
  border-radius: 12px !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}

/* تخصيص شريط التحميل */
[data-testid="progress-bar"] {
  background: var(--primary-color) !important;
  border-radius: 4px !important;
}

/* تخصيص التبديل */
[data-testid="switch"][data-state="checked"] {
  background: var(--primary-color) !important;
}

/* تخصيص التبويبات */
[data-testid="tabs"] [data-state="active"] {
  color: var(--primary-color) !important;
  border-bottom-color: var(--primary-color) !important;
}

/* تخصيص الأيقونات */
svg {
  color: inherit !important;
}

/* تخصيص النصوص */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary) !important;
  font-weight: 600 !important;
}

p, span, div {
  color: var(--text-secondary) !important;
}

/* تخصيص الروابط */
a {
  color: var(--primary-color) !important;
  text-decoration: none !important;
}

a:hover {
  color: var(--primary-dark) !important;
  text-decoration: underline !important;
}

/* تخصيص الخلفية الرئيسية */
main {
  background: #fafbfc !important;
  min-height: 100vh !important;
}

/* تحسينات للجوال */
@media (max-width: 768px) {
  [data-testid="sidebar"] {
    width: 100% !important;
  }
  
  table {
    font-size: 14px !important;
  }
  
  .stats-card {
    padding: 16px !important;
  }
  
  .stats-card .stats-value {
    font-size: 1.5rem !important;
  }
}
