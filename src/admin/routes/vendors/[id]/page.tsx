import { defineRouteConfig } from "@medusajs/admin-sdk"
import { Container, Heading, Button, Badge, Card, Text } from "@medusajs/ui"
import { useState, useEffect } from "react"
import { useParams } from "react-router-dom"

const VendorDetailsPage = () => {
  const { id } = useParams()
  const [vendor, setVendor] = useState<any>(null)
  const [products, setProducts] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (id) {
      fetchVendorDetails()
      fetchVendorProducts()
    }
  }, [id])

  const fetchVendorDetails = async () => {
    try {
      const response = await fetch(`/admin/vendors/${id}`)
      const data = await response.json()
      setVendor(data.vendor)
    } catch (error) {
      console.error('Error fetching vendor details:', error)
    }
  }

  const fetchVendorProducts = async () => {
    try {
      const response = await fetch(`/admin/vendors/${id}/products`)
      const data = await response.json()
      setProducts(data.products || [])
    } catch (error) {
      console.error('Error fetching vendor products:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleApprove = async () => {
    try {
      await fetch(`/admin/vendors/${id}/approve`, {
        method: 'POST',
      })
      fetchVendorDetails()
    } catch (error) {
      console.error('Error approving vendor:', error)
    }
  }

  const handleReject = async () => {
    try {
      await fetch(`/admin/vendors/${id}/reject`, {
        method: 'POST',
      })
      fetchVendorDetails()
    } catch (error) {
      console.error('Error rejecting vendor:', error)
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: { variant: "orange" as const, text: "في انتظار الموافقة" },
      approved: { variant: "green" as const, text: "مُوافق عليه" },
      rejected: { variant: "red" as const, text: "مرفوض" },
      suspended: { variant: "grey" as const, text: "معلق" },
    }
    const config = variants[status as keyof typeof variants] || variants.pending
    return <Badge variant={config.variant}>{config.text}</Badge>
  }

  if (loading || !vendor) {
    return (
      <Container className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-48"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </Container>
    )
  }

  return (
    <Container className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex space-x-2 space-x-reverse">
          <Button
            variant="secondary"
            onClick={() => window.history.back()}
          >
            رجوع
          </Button>
          {vendor.status === 'pending' && (
            <>
              <Button
                variant="primary"
                onClick={handleApprove}
                className="bg-green-600 hover:bg-green-700"
              >
                موافقة على البائع
              </Button>
              <Button
                variant="danger"
                onClick={handleReject}
              >
                رفض البائع
              </Button>
            </>
          )}
        </div>
        <div className="text-right">
          <Heading level="h1">{vendor.name}</Heading>
          <div className="mt-1">{getStatusBadge(vendor.status)}</div>
        </div>
      </div>

      {/* Vendor Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <Heading level="h3" className="mb-4 text-right">المعلومات الشخصية</Heading>
          <div className="space-y-3 text-right">
            <div>
              <Text className="font-medium">الاسم الكامل:</Text>
              <Text className="text-gray-600">{vendor.name}</Text>
            </div>
            <div>
              <Text className="font-medium">البريد الإلكتروني:</Text>
              <Text className="text-gray-600">{vendor.email}</Text>
            </div>
            <div>
              <Text className="font-medium">رقم الهاتف:</Text>
              <Text className="text-gray-600">{vendor.phone || 'غير محدد'}</Text>
            </div>
            <div>
              <Text className="font-medium">تاريخ التسجيل:</Text>
              <Text className="text-gray-600">
                {new Date(vendor.created_at).toLocaleDateString('ar-SA')}
              </Text>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <Heading level="h3" className="mb-4 text-right">معلومات المتجر</Heading>
          <div className="space-y-3 text-right">
            <div>
              <Text className="font-medium">اسم المتجر:</Text>
              <Text className="text-gray-600">{vendor.store_name}</Text>
            </div>
            <div>
              <Text className="font-medium">وصف المتجر:</Text>
              <Text className="text-gray-600">{vendor.store_description || 'غير محدد'}</Text>
            </div>
            <div>
              <Text className="font-medium">العنوان:</Text>
              <Text className="text-gray-600">{vendor.store_address || 'غير محدد'}</Text>
            </div>
            <div>
              <Text className="font-medium">المدينة:</Text>
              <Text className="text-gray-600">{vendor.store_city || 'غير محدد'}</Text>
            </div>
            <div>
              <Text className="font-medium">الدولة:</Text>
              <Text className="text-gray-600">{vendor.store_country || 'غير محدد'}</Text>
            </div>
          </div>
        </Card>
      </div>

      {/* Business Information */}
      <Card className="p-6">
        <Heading level="h3" className="mb-4 text-right">معلومات العمل</Heading>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-right">
          <div>
            <Text className="font-medium">رخصة العمل:</Text>
            <Text className="text-gray-600">{vendor.business_license || 'غير محدد'}</Text>
          </div>
          <div>
            <Text className="font-medium">الرقم الضريبي:</Text>
            <Text className="text-gray-600">{vendor.tax_id || 'غير محدد'}</Text>
          </div>
        </div>
      </Card>

      {/* Products */}
      <Card className="p-6">
        <Heading level="h3" className="mb-4 text-right">
          منتجات البائع ({products.length})
        </Heading>
        {products.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            لم يقم البائع بإضافة أي منتجات بعد
          </div>
        ) : (
          <div className="space-y-3">
            {products.slice(0, 5).map((product: any) => (
              <div key={product.id} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                <div className="text-right">
                  <Text className="font-medium">{product.title}</Text>
                  <Text className="text-sm text-gray-600">
                    {product.status === 'published' ? 'منشور' : 'مسودة'}
                  </Text>
                </div>
                <Button
                  variant="transparent"
                  size="small"
                  onClick={() => window.location.href = `/app/products/${product.id}`}
                >
                  عرض
                </Button>
              </div>
            ))}
            {products.length > 5 && (
              <div className="text-center pt-3">
                <Button variant="secondary" size="small">
                  عرض جميع المنتجات ({products.length})
                </Button>
              </div>
            )}
          </div>
        )}
      </Card>
    </Container>
  )
}

export const config = defineRouteConfig({
  label: "تفاصيل البائع",
})

export default VendorDetailsPage
