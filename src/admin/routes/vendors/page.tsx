import { defineRouteConfig } from "@medusajs/admin-sdk"
import { Container, Heading, Button, Badge, Table } from "@medusajs/ui"
import { useState, useEffect } from "react"
import { Pencil<PERSON>quare, Trash, Eye } from "@medusajs/icons"

const VendorsPage = () => {
  const [vendors, setVendors] = useState([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState('all')

  useEffect(() => {
    fetchVendors()
  }, [])

  const fetchVendors = async () => {
    try {
      const response = await fetch('/admin/vendors')
      const data = await response.json()
      setVendors(data.vendors || [])
    } catch (error) {
      console.error('Error fetching vendors:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleApprove = async (vendorId: string) => {
    try {
      await fetch(`/admin/vendors/${vendorId}/approve`, {
        method: 'POST',
      })
      fetchVendors()
    } catch (error) {
      console.error('Error approving vendor:', error)
    }
  }

  const handleReject = async (vendorId: string) => {
    try {
      await fetch(`/admin/vendors/${vendorId}/reject`, {
        method: 'POST',
      })
      fetchVendors()
    } catch (error) {
      console.error('Error rejecting vendor:', error)
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: { variant: "orange" as const, text: "في انتظار الموافقة" },
      approved: { variant: "green" as const, text: "مُوافق عليه" },
      rejected: { variant: "red" as const, text: "مرفوض" },
      suspended: { variant: "grey" as const, text: "معلق" },
    }
    const config = variants[status as keyof typeof variants] || variants.pending
    return <Badge variant={config.variant}>{config.text}</Badge>
  }

  const filteredVendors = vendors.filter((vendor: any) => {
    if (filter === 'all') return true
    return vendor.status === filter
  })

  if (loading) {
    return (
      <Container className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-48"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </Container>
    )
  }

  return (
    <Container className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex space-x-2 space-x-reverse">
          <Button
            variant={filter === 'all' ? 'primary' : 'secondary'}
            onClick={() => setFilter('all')}
            size="small"
          >
            الكل ({vendors.length})
          </Button>
          <Button
            variant={filter === 'pending' ? 'primary' : 'secondary'}
            onClick={() => setFilter('pending')}
            size="small"
          >
            معلق ({vendors.filter((v: any) => v.status === 'pending').length})
          </Button>
          <Button
            variant={filter === 'approved' ? 'primary' : 'secondary'}
            onClick={() => setFilter('approved')}
            size="small"
          >
            مُوافق عليه ({vendors.filter((v: any) => v.status === 'approved').length})
          </Button>
        </div>
        <Heading level="h1" className="text-right">
          إدارة البائعين
        </Heading>
      </div>

      <div className="bg-white rounded-lg shadow">
        <Table>
          <Table.Header>
            <Table.Row>
              <Table.HeaderCell className="text-right">الإجراءات</Table.HeaderCell>
              <Table.HeaderCell className="text-right">الحالة</Table.HeaderCell>
              <Table.HeaderCell className="text-right">المدينة</Table.HeaderCell>
              <Table.HeaderCell className="text-right">اسم المتجر</Table.HeaderCell>
              <Table.HeaderCell className="text-right">البريد الإلكتروني</Table.HeaderCell>
              <Table.HeaderCell className="text-right">اسم البائع</Table.HeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {filteredVendors.map((vendor: any) => (
              <Table.Row key={vendor.id}>
                <Table.Cell>
                  <div className="flex space-x-2 space-x-reverse">
                    <Button
                      variant="transparent"
                      size="small"
                      onClick={() => window.location.href = `/app/vendors/${vendor.id}`}
                    >
                      <Eye />
                    </Button>
                    {vendor.status === 'pending' && (
                      <>
                        <Button
                          variant="transparent"
                          size="small"
                          onClick={() => handleApprove(vendor.id)}
                          className="text-green-600 hover:text-green-700"
                        >
                          موافقة
                        </Button>
                        <Button
                          variant="transparent"
                          size="small"
                          onClick={() => handleReject(vendor.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          رفض
                        </Button>
                      </>
                    )}
                  </div>
                </Table.Cell>
                <Table.Cell>
                  {getStatusBadge(vendor.status)}
                </Table.Cell>
                <Table.Cell className="text-right">
                  {vendor.store_city || '-'}
                </Table.Cell>
                <Table.Cell className="text-right font-medium">
                  {vendor.store_name}
                </Table.Cell>
                <Table.Cell className="text-right">
                  {vendor.email}
                </Table.Cell>
                <Table.Cell className="text-right font-medium">
                  {vendor.name}
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>

        {filteredVendors.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500 mb-2">لا توجد بائعين</div>
            <div className="text-sm text-gray-400">
              {filter === 'all' 
                ? 'لم يتم تسجيل أي بائعين بعد'
                : `لا توجد بائعين بحالة "${filter}"`
              }
            </div>
          </div>
        )}
      </div>
    </Container>
  )
}

export const config = defineRouteConfig({
  label: "البائعين",
  icon: "users",
})

export default VendorsPage
