// تخصيص لوحة الإدارة للمتجر الجزائري
(function() {
  'use strict';

  // إعدادات المتجر الجزائري
  const STORE_CONFIG = {
    currency: 'DZD',
    currencySymbol: 'دج',
    country: 'الجزائر',
    language: 'ar',
    timezone: 'Africa/Algiers'
  };

  // تحديث النصوص للعربية
  const ARABIC_TRANSLATIONS = {
    'Dashboard': 'لوحة التحكم',
    'Orders': 'الطلبات',
    'Products': 'المنتجات',
    'Customers': 'العملاء',
    'Vendors': 'البائعين',
    'Settings': 'الإعدادات',
    'Analytics': 'التحليلات',
    'Inventory': 'المخزون',
    'Categories': 'الفئات',
    'Collections': 'المجموعات',
    'Discounts': 'الخصومات',
    'Users': 'المستخدمين',
    'Team': 'الفريق',
    'Profile': 'الملف الشخصي',
    'Logout': 'تسجيل الخروج',
    'Search': 'بحث',
    'Add': 'إضافة',
    'Edit': 'تعديل',
    'Delete': 'حذف',
    'Save': 'حفظ',
    'Cancel': 'إلغاء',
    'Status': 'الحالة',
    'Active': 'نشط',
    'Inactive': 'غير نشط',
    'Pending': 'معلق',
    'Approved': 'موافق عليه',
    'Rejected': 'مرفوض',
    'Total': 'المجموع',
    'Price': 'السعر',
    'Quantity': 'الكمية',
    'Name': 'الاسم',
    'Email': 'البريد الإلكتروني',
    'Phone': 'الهاتف',
    'Address': 'العنوان',
    'City': 'المدينة',
    'Date': 'التاريخ',
    'Actions': 'الإجراءات',
    'View': 'عرض',
    'Details': 'التفاصيل',
    'Description': 'الوصف',
    'Image': 'الصورة',
    'Category': 'الفئة',
    'Stock': 'المخزون',
    'Available': 'متوفر',
    'Out of Stock': 'نفد من المخزون',
    'Low Stock': 'مخزون منخفض'
  };

  // دالة ترجمة النصوص
  function translateText(text) {
    return ARABIC_TRANSLATIONS[text] || text;
  }

  // دالة تنسيق العملة
  function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 2
    }).format(amount);
  }

  // دالة تنسيق التاريخ
  function formatDate(date) {
    return new Intl.DateTimeFormat('ar-DZ', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      timeZone: 'Africa/Algiers'
    }).format(new Date(date));
  }

  // دالة إخفاء العناصر غير المطلوبة
  function hideUnwantedElements() {
    const unwantedSelectors = [
      '[href*="gift-cards"]',
      '[href*="promotions"]',
      '[href*="campaigns"]',
      '[href*="taxes"]',
      '[href*="regions"]',
      '[href*="currencies"]',
      '[href*="sales-channels"]',
      '[href*="api-key-management"]',
      '[href*="publishable-api-keys"]',
      '[href*="workflows"]',
      '[data-testid*="gift-card"]',
      '[data-testid*="promotion"]',
      '[data-testid*="campaign"]',
      '[data-testid*="tax"]',
      '[data-testid*="region"]',
      '[data-testid*="currency"]',
      '[data-testid*="sales-channel"]',
      '[data-testid*="api-key"]',
      '[data-testid*="workflow"]'
    ];

    unwantedSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        element.style.display = 'none';
      });
    });
  }

  // دالة تطبيق الترجمة على العناصر
  function applyTranslations() {
    // ترجمة النصوص في القوائم
    const menuItems = document.querySelectorAll('[data-testid="nav-item"] span, [data-testid="sidebar"] a span');
    menuItems.forEach(item => {
      const originalText = item.textContent.trim();
      const translatedText = translateText(originalText);
      if (translatedText !== originalText) {
        item.textContent = translatedText;
      }
    });

    // ترجمة الأزرار
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
      const originalText = button.textContent.trim();
      const translatedText = translateText(originalText);
      if (translatedText !== originalText) {
        button.textContent = translatedText;
      }
    });

    // ترجمة العناوين
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headings.forEach(heading => {
      const originalText = heading.textContent.trim();
      const translatedText = translateText(originalText);
      if (translatedText !== originalText) {
        heading.textContent = translatedText;
      }
    });

    // ترجمة رؤوس الجداول
    const tableHeaders = document.querySelectorAll('th');
    tableHeaders.forEach(header => {
      const originalText = header.textContent.trim();
      const translatedText = translateText(originalText);
      if (translatedText !== originalText) {
        header.textContent = translatedText;
      }
    });
  }

  // دالة تحديث تنسيق العملة
  function updateCurrencyFormat() {
    const priceElements = document.querySelectorAll('[data-testid*="price"], .price, [class*="price"]');
    priceElements.forEach(element => {
      const text = element.textContent;
      const priceMatch = text.match(/[\d,]+\.?\d*/);
      if (priceMatch) {
        const price = parseFloat(priceMatch[0].replace(/,/g, ''));
        element.textContent = text.replace(priceMatch[0], formatCurrency(price));
      }
    });
  }

  // دالة تحديث تنسيق التاريخ
  function updateDateFormat() {
    const dateElements = document.querySelectorAll('[data-testid*="date"], .date, [class*="date"]');
    dateElements.forEach(element => {
      const text = element.textContent;
      const dateMatch = text.match(/\d{4}-\d{2}-\d{2}/);
      if (dateMatch) {
        const formattedDate = formatDate(dateMatch[0]);
        element.textContent = text.replace(dateMatch[0], formattedDate);
      }
    });
  }

  // دالة إضافة معلومات المتجر الجزائري
  function addStoreInfo() {
    // إضافة معلومات العملة في الهيدر
    const header = document.querySelector('header');
    if (header && !document.querySelector('.store-currency-info')) {
      const currencyInfo = document.createElement('div');
      currencyInfo.className = 'store-currency-info';
      currencyInfo.style.cssText = `
        position: absolute;
        top: 10px;
        left: 20px;
        background: var(--primary-color);
        color: white;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      `;
      currencyInfo.textContent = `${STORE_CONFIG.country} - ${STORE_CONFIG.currencySymbol}`;
      header.appendChild(currencyInfo);
    }
  }

  // دالة تحسين تجربة المستخدم
  function enhanceUserExperience() {
    // إضافة تأثيرات التحويم
    const cards = document.querySelectorAll('[data-testid="card"], .card');
    cards.forEach(card => {
      card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-2px)';
        this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
      });
      
      card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
      });
    });

    // تحسين الأزرار
    const buttons = document.querySelectorAll('button[data-variant="primary"]');
    buttons.forEach(button => {
      button.addEventListener('mouseenter', function() {
        this.style.transform = 'scale(1.02)';
      });
      
      button.addEventListener('mouseleave', function() {
        this.style.transform = 'scale(1)';
      });
    });
  }

  // دالة التهيئة الرئيسية
  function initialize() {
    // تطبيق التخصيصات
    hideUnwantedElements();
    applyTranslations();
    updateCurrencyFormat();
    updateDateFormat();
    addStoreInfo();
    enhanceUserExperience();

    // إعادة تطبيق التخصيصات عند تغيير المحتوى
    const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          setTimeout(() => {
            hideUnwantedElements();
            applyTranslations();
            updateCurrencyFormat();
            updateDateFormat();
            enhanceUserExperience();
          }, 100);
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  // تشغيل التخصيصات عند تحميل الصفحة
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize);
  } else {
    initialize();
  }

  // تشغيل التخصيصات عند تغيير الصفحة (SPA)
  let currentUrl = location.href;
  new MutationObserver(() => {
    if (location.href !== currentUrl) {
      currentUrl = location.href;
      setTimeout(initialize, 500);
    }
  }).observe(document, { subtree: true, childList: true });

})();
