import { Migration } from '@mikro-orm/migrations';

export class Migration20250708151059 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "vendor" add column if not exists "store_currency" text not null default 'DZD';`);
    this.addSql(`alter table if exists "vendor" alter column "store_country" type text using ("store_country"::text);`);
    this.addSql(`alter table if exists "vendor" alter column "store_country" set default 'الجزائر';`);
    this.addSql(`alter table if exists "vendor" alter column "store_country" set not null;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table if exists "vendor" drop column if exists "store_currency";`);

    this.addSql(`alter table if exists "vendor" alter column "store_country" drop default;`);
    this.addSql(`alter table if exists "vendor" alter column "store_country" type text using ("store_country"::text);`);
    this.addSql(`alter table if exists "vendor" alter column "store_country" drop not null;`);
  }

}
