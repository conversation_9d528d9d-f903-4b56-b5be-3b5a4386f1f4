import { Migration } from '@mikro-orm/migrations';

export class Migration20250708142751 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "vendor" drop constraint if exists "vendor_email_unique";`);
    this.addSql(`create table if not exists "vendor" ("id" text not null, "name" text not null, "email" text not null, "phone" text null, "description" text null, "logo_url" text null, "banner_url" text null, "store_name" text not null, "store_description" text null, "store_address" text null, "store_city" text null, "store_country" text null, "store_postal_code" text null, "business_license" text null, "tax_id" text null, "status" text check ("status" in ('pending', 'approved', 'rejected', 'suspended')) not null default 'pending', "is_active" boolean not null default true, "password_hash" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "vendor_pkey" primary key ("id"));`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_vendor_email_unique" ON "vendor" (email) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_vendor_deleted_at" ON "vendor" (deleted_at) WHERE deleted_at IS NULL;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "vendor" cascade;`);
  }

}
