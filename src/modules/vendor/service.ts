import { MedusaService } from "@medusajs/framework/utils"
import Vendor from "./models/vendor"

class VendorModuleService extends MedusaService({
  Vendor,
}) {
  // Create a new vendor
  async createVendor(data: any) {
    return await this.create(data)
  }

  // Get vendor by ID
  async getVendor(id: string) {
    return await this.retrieve(id)
  }

  // Get vendor by email
  async getVendorByEmail(email: string) {
    return await this.list({ email })
  }

  // List all vendors
  async listVendors(filters = {}, config = {}) {
    return await this.listAndCount(filters, config)
  }

  // Update vendor
  async updateVendor(id: string, data: any) {
    return await this.update(id, data)
  }

  // Delete vendor
  async deleteVendor(id: string) {
    return await this.delete(id)
  }

  // Approve vendor
  async approveVendor(id: string) {
    return await this.update(id, { status: "approved" })
  }

  // Reject vendor
  async rejectVendor(id: string) {
    return await this.update(id, { status: "rejected" })
  }

  // Suspend vendor
  async suspendVendor(id: string) {
    return await this.update(id, { status: "suspended" })
  }

  // Activate vendor
  async activateVendor(id: string) {
    return await this.update(id, { is_active: true })
  }

  // Deactivate vendor
  async deactivateVendor(id: string) {
    return await this.update(id, { is_active: false })
  }
}

export default VendorModuleService
