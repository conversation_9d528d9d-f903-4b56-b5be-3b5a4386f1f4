# 🚀 دليل التشغيل السريع - ZEDINGO

## التشغيل السريع

```bash
# تشغيل المشروع بأمر واحد
./start.sh
```

## أو التشغيل اليدوي

### 1. إعداد قاعدة البيانات
```bash
npm run db:create
npm run db:migrate
npm run setup-algeria
```

### 2. تشغيل التطبيقات
```bash
# تشغيل جميع التطبيقات معاً
npm run dev-all

# أو تشغيل كل تطبيق منفصلاً
npm run dev                    # Backend
cd vendor-dashboard && npm run dev  # لوحة البائعين
cd storefront && npm run dev        # واجهة المتجر
```

## الوصول للتطبيقات

| التطبيق | الرابط | الوصف |
|---------|--------|-------|
| 🏪 واجهة المتجر | http://localhost:3000 | للعملاء والتسوق |
| 👥 لوحة البائعين | http://localhost:3001 | لإدارة المنتجات |
| 🛡️ لوحة الإدارة | http://localhost:9000/admin_95 | للمسؤولين |
| 🔧 API | http://localhost:9000 | واجهة برمجية |

## بيانات تجريبية

### مستخدم إداري
- البريد: `<EMAIL>`
- كلمة المرور: (سيتم إنشاؤها تلقائياً)

### تسجيل بائع جديد
1. اذهب إلى http://localhost:3001/register
2. املأ البيانات المطلوبة
3. انتظر موافقة الإدارة من لوحة الإدارة

## المميزات الرئيسية

### ✅ مكتمل
- ✅ نظام البائعين متعدد المستويات
- ✅ لوحة إدارة مبسطة باللغة العربية
- ✅ واجهة متجر عصرية
- ✅ دعم العملة الجزائرية (دج)
- ✅ الدفع عند الاستلام فقط
- ✅ تصميم متجاوب

### 🔧 قابل للتطوير
- إضافة المزيد من طرق الشحن
- تحسين نظام البحث
- إضافة تقييمات المنتجات
- نظام الإشعارات

## استكشاف الأخطاء

### مشكلة قاعدة البيانات
```bash
# إعادة إنشاء قاعدة البيانات
dropdb medusa-store
npm run db:create
npm run db:migrate
npm run setup-algeria
```

### مشكلة في المنافذ
```bash
# التحقق من المنافذ المستخدمة
lsof -i :3000
lsof -i :3001
lsof -i :9000

# إيقاف العمليات
kill -9 <PID>
```

### مشكلة في التبعيات
```bash
# إعادة تثبيت التبعيات
rm -rf node_modules package-lock.json
npm install

cd vendor-dashboard
rm -rf node_modules package-lock.json
npm install

cd ../storefront
rm -rf node_modules package-lock.json
npm install
```

## الدعم

للحصول على المساعدة:
- راجع ملف README.md للتفاصيل الكاملة
- تحقق من ملفات السجل في كل تطبيق
- تأكد من تشغيل PostgreSQL

---

🇩🇿 **ZEDINGO** - متجر إلكتروني جزائري متعدد البائعين
